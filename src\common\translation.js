// avoid using en and cn as keys(except for leaf nodes) in the translation file


export const text = {
	common: {
		exitUploadCAD: {
			en: 'Cancel Upload CAD',
			cn: '取消上传CAD',
		},
		textComparison: {
			en: 'Text Comparison',
			cn: '文本比较',
		},
		solder: {
			en: 'Solder',
			cn: '焊料',
		},
		pad: {
			en: 'Pad',
			cn: '焊盘',
		},
		tip: {
			en: 'Tip',
			cn: '焊珠',
		},
		loading: {
			en: 'Loading...',
			cn: '加载中...',
		},
		action: {
			en: 'Action',
			cn: '操作',
		},
		exportAsFile: {
			en: 'Export as File',
			cn: '导出为文件',
		},
		displayPesdoColor: {
			en: 'Display Pseudo Color',
			cn: '显示伪彩色',
		},
		noData: {
			en: 'No Data',
			cn: '无数据',
		},
                disabled: {
                        en: 'Disabled',
                        cn: '已禁用',
                },
                none: {
                        en: 'None',
                        cn: '无',
                },
                unknown: {
                        en: 'Unknown',
                        cn: '未知',
                },
		undefined: {
			en: 'Undefined',
			cn: '未定义',
		},
		failed: {
			en: 'Failed',
			cn: '已失败',
		},
		passed: {
			en: 'Passed',
			cn: '已通过',
		},
		information: {
			en: 'Information',
			cn: '信息',
		},
		subBoard: {
			en: 'Sub Board',
			cn: '子板',
		},
		close: {
			en: 'Close',
			cn: '关闭',
		},
		viewUpdateColorRange: {
			en: 'View/Update',
			cn: '查看/更新',
		},
		color: {
			en: 'Color',
			cn: '颜色',
		},
		finish: {
			en: 'Finish',
			cn: '完成',
		},
		confirm: {
			en: 'Confirm',
			cn: '确认',
		},
		quit: {
			en: 'Quit',
			cn: '退出',
		},
		next: {
			en: 'Next',
			cn: '下一步',
		},
		unknownPackageNo: {
			en: 'Unknown Package No',
			cn: '未知封装',
		},
		unknownPartNo: {
			en: 'Unknown Part No',
			cn: '未知料号',
		},
		exportMESResult: {
			en: 'Export MES Result',
			cn: '导出MES结果',
		},
		export: {
			en: 'Export',
			cn: '导出',
		},
                threshold: {
                        en: 'Threshold',
                        cn: '阈值',
                },
                viewStats: {
                        en: 'View Stats',
                        cn: '查看统计',
                },
                paramValue: {
                        en: 'Parameter Value',
                        cn: '参数值',
                },
                aiDeviationScore: {
                        en: 'AI Deviation Score',
                        cn: 'AI偏差分数',
                },
                percent: {
                        en: 'Percent (%)',
                        cn: '百分比 (%)',
                },
                count: {
                        en: 'Count',
                        cn: '计数',
                },
                recommendedThresholdValue: {
                        en: 'Recommended threshold value:',
                        cn: '推荐阈值：',
                },
                ADMIN: {
                        en: 'Admin',
                        cn: '管理员',
                },
		OPERATOR: {
			en: 'Operator',
			cn: '操作员',
		},
		PROGRAMMER: {
			en: 'Programmer',
			cn: '编程人员',
		},
		logout: {
			en: 'Logout',
			cn: '登出',
		},
		proceedToEditComponents: {
			en: 'Save and Proceed to edit components',
			cn: '保存并继续编辑元件',
		},
		permissionRestrictedOrLoginHasExpired: {
			en: 'Permission restricted or login has expired. Please login again.',
			cn: '权限受限或登录已过期。请重新登录。',
		},
		removePCBA: {
			en: 'Remove PCB',
			cn: '删除PCB',
		},
                thisActionWillRemovePCBA: {
                        en: 'This action will remove the PCB ${productName} from the system. Are you sure you want to proceed?',
                        cn: '此操作将从系统中删除PCB ${productName}。您确定要继续吗？',
                },
                deleteTemplate: {
                        en: 'Delete Template',
                        cn: '删除模板',
                },
                thisActionWillDeleteTemplate: {
                        en: 'This action will delete the template ${templateName} from your library. Are you sure you want to proceed?',
                        cn: '此操作将从您的库中删除模板 ${templateName}。您确定要继续吗？',
                },
                resetView: {
                        en: 'Reset View',
                        cn: '重置视图',
                },
		retrainNow: {
			en: 'Retrain Now',
			cn: '立即训练',
		},
		ignore: {
			en: 'Ignore',
			cn: '忽略',
		},
		rename: {
			en: 'Rename',
			cn: '重命名',
		},
		result: {
			en: 'Result',
			cn: '结果',
		},
		reference: {
			en: 'Reference',
			cn: '基准',
		},
		inference: {
			en: 'Inference',
			cn: '推理',
		},
		systemLanguage: {
			en: 'System Language',
			cn: '系统语言',
		},
		systemLanguageDesc: {
			en: 'The system language is set and will be saved in the system. It will be used when the system is restarted.',
			cn: '系统语言已设置并将保存在系统中。重启系统时将使用该语言。',
		},
		language: {
			en: 'Language',
			cn: '语言',
		},
		english: {
			en: 'English',
			cn: '英语',
		},
		chinese: {
			en: 'Chinese',
			cn: '中文',
		},
		off: {
			en: 'Off',
			cn: '关闭',
		},
		minimum: {
			en: 'Minimum',
			cn: '最小值',
		},
		maximum: {
			en: 'Maximum',
			cn: '最大值',
		},
		apply: {
			en: 'Apply',
			cn: '应用',
		},
		automatic: {
			en: 'Automatic',
			cn: '自动',
		},
		manual: {
			en: 'Manual',
			cn: '手动',
		},
		login: {
			en: 'Login',
			cn: '登录',
		},
		daoaiAOISystem: {
			en: 'DaoAI AOI System',
			cn: 'DaoAI AOI System',
		},
		systemStatus: {
			en: 'System Status:',
			cn: '系统状态：',
		},
		task: {
			en: 'Task',
			cn: '任务',
		},
		pass: {
			en: 'Pass',
			cn: '合格',
		},
		search: {
			en: 'Search',
			cn: '搜索',
		},
		total: {
			en: 'Total',
			cn: '总数',
		},
		remove: {
			en: 'Remove',
			cn: '删除',
		},
		cancel: {
			en: 'Cancel',
			cn: '取消',
		},
		save: {
			en: 'Save',
			cn: '保存',
		},
		set: {
			en: 'Set',
			cn: '设置',
		},
		capture: {
			en: 'Capture',
			cn: '拍摄',
		},
		delete: {
			en: 'Delete',
			cn: '删除',
		},
		all: {
			en: 'All',
			cn: '全部',
		},
		twoD: {
			en: '2D',
			cn: '2D',
		},
		threeD: {
			en: '3D',
			cn: '3D',
		},
		test: {
			en: 'Test',
			cn: '评估',
		},
		component: {
			en: 'Component',
			cn: '元件',
		},
		reminder: {
			en: 'Reminder',
			cn: '提示',
		},
		ok: {
			en: 'OK',
			cn: '确定',
		},
		donNotShowAgain: {
			en: "Don't show again",
			cn: '不再显示',
		},
		undefined: {
			en: 'Undefined',
			cn: '未定义',
		},
		measure: {
			en: 'Measure',
			cn: '测量',
		},
		lastSaved: {
			en: 'Last Saved',
			cn: '最后保存',
		},
		reset: {
			en: 'Reset',
			cn: '重置',
		},
		threeDView: {
			en: '3D View',
			cn: '3D视图',
		},
		copy: {
			en: 'Copy',
			cn: '复制',
		},
		saveAndContinue: {
			en: 'Save and Continue',
			cn: '保存并继续',
		},
		depthRange: {
			en: 'Depth Range',
			cn: '深度范围',
		},
		continue: {
			en: 'Continue',
			cn: '继续',
		},
		selectFile: {
			en: 'Select File',
			cn: '选择文件',
		},
		previous: {
			en: 'Previous',
			cn: '上一步',
		},
		edit: {
			en: 'Edit',
			cn: '编辑',
		},
		search: {
			en: 'Search',
			cn: '搜索',
		},
		translate: {
			en: 'Translate',
			cn: '平移',
		},
		drawRoi: {
			en: 'Add ROI into group',
			cn: '在组中添加ROI',
		},
		editInfo: {
			en: 'Edit Info',
			cn: '编辑信息',
		},
		failToLoadSystemMetadata: {
			en: 'Fail to load system metadata, please check the server status.',
			cn: '加载系统元数据失败, 请检查服务器状态。',
		},
		refresh: {
			en: 'Refresh',
			cn: '刷新',
		},
		fetchingSystemMetadata: {
			en: 'Fetching system metadata...',
			cn: '正在获取系统元数据...',
		},
		setRoi: {
			en: 'Set ROI',
			cn: '设置ROI',
		},
		showRoi: {
			en: 'Show ROI',
			cn: '显示ROI',
		},
		initRoi: {
			en: 'Init ROI',
			cn: '初始化ROI',
		},
		setExtendedRoi: {
			en: 'Set Extended ROI',
			cn: '设置扩展ROI',
		},
		min: {
			en: 'min',
			cn: '最小值',
		},
		max: {
			en: 'max',
			cn: '最大值',
		},
		setProfileRoi: {
			en: 'Set Profile ROI',
			cn: '设置轮廓ROI',
		},
		actions: {
			en: 'Actions',
			cn: '操作',
		},
		more: {
			en: 'More',
			cn: '更多',
		},
		created: {
			en: 'Created',
			cn: '创建时间',
		},
		package: {
			en: 'Package',
			cn: '封装',
		},
		part: {
			en: 'Part',
			cn: '料号',
		},
	},
	allUpperCases: {
    packageNo: {
      en: 'PACKAGE NO',
      cn: '包装号',
    },
		settings: {
			en: 'SETTINGS',
			cn: '设置',
		},
		worklist: {
			en: 'WORKLIST',
			cn: '工作列表',
		},
		translate: {
			en: 'TRANSLATE',
			cn: '平移',
		},
		rotate: {
			en: 'ROTATE',
			cn: '旋转',
		},
		parts: {
			en: 'PARTS',
			cn: '部件',
		},
		partNumber: {
			en: 'PART NUMBER',
			cn: '料号',
		},
		createdAt: {
			en: 'CREATED AT',
			cn: '创建时间',
		},
	},
	login: {
		username: {
			en: 'Username',
			cn: '用户名',
		},
		password: {
			en: 'Password',
			cn: '密码',
		},
		changeHostAddress: {
			en: 'Change Host Address',
			cn: '更改主机地址',
		},

	},
	home: {
		newPCBAName: {
			en: 'New PCB Name',
			cn: '新PCB名称',
		},
		updatePCBAName: {
			en: 'Update PCB Name',
			cn: '更新PCB名称',
		},
		inspecting: {
			en: 'Inspecting',
			cn: '检测中',
		},
		programming: {
			en: 'Programming',
			cn: '编程中',
		},
		refreshConveyorStatus: {
			en: 'Refresh conveyor status',
			cn: '刷新传送带状态',
		},
		definingPCB: {
			en: 'Defining PCB',
			cn: '定义PCB中',
		},
		idle: {
			en: 'Idle',
			cn: '空闲',
		},
		runningInspectionTask: {
			en: 'Running Inspection Task',
			cn: '运行检测任务中',
		},
		sessionId: {
			en: 'Session ID',
			cn: '检测任务ID',
		},
		newInspectionTask: {
			en: 'New Inspection Task',
			cn: '新建检测任务',
		},
		pickAPCB: {
			en: 'Pick a PCB to begin inspection',
			cn: '选择一个PCB开始检测',
		},
		teachPCB: {
			en: 'Teach PCB',
			cn: '训练PCB',
		},
		registerNewPCB: {
			en: 'Register new PCB/Product',
			cn: '注册新PCB/产品',
		},
		workList: {
			en: 'Work List',
			cn: '工作列表',
		},
		reviewLast: {
			en: 'Review past inspection sessions',
			cn: '查看过去的检测记录',
		},
		recentInspectionTask: {
			en: 'Recent Inspection Task',
			cn: '最近的检测任务',
		},
		conveyorFront: {
			en: 'Conveyor #A',
			cn: '传送带 #A',
		},
		conveyorBack: {
			en: 'Conveyor #B',
			cn: '传送带 #B',
		},
		currentlyInspecting: {
			en: 'Currently Inspecting',
			cn: '当前检测',
		},
		totalProduct: {
			en: 'Total product',
			cn: '总产品数',
		},
		passRate: {
			en: 'Pass Rate',
			cn: '合格率',
		},
		startedAt: {
			en: 'Started at',
			cn: '开始时间',
		},
		PCBType: {
			en: 'PCB Type',
			cn: 'PCB类型',
		},
		lastUpdated: {
			en: 'Last Updated',
			cn: '最后更新',
		},
		managePCB: {
			en: 'Manage PCB',
			cn: '管理PCB',
		},
	},
	settings: {
		language: {
			en: 'Language',
			cn: '语言',
		},
		host: {
			en: 'Host',
			cn: '主机',
		},
		systemConfig: {
			en: 'System Config',
			cn: '系统配置',
		},
		systemSettings: {
			en: 'System Settings',
			cn: '系统设置',
		},
		team: {
			en: 'Team',
			cn: '团队',
		},
		members: {
			en: 'Members',
			cn: '成员',
		},
		addProgrammer: {
			en: 'Add Programmer',
			cn: '添加编程人员',
		},
		teachPCB: {
			en: 'Teach PCB, and operate inspection task',
			cn: '训练PCB，并操作检测任务',
		},
		remaining: {
			en: 'seat',
			cn: '个名额',
		},
		addOperator: {
			en: 'Add Operator',
			cn: '添加操作员',
		},
		onlyCanOperate: {
			en: 'Only can operate and review inspection task',
			cn: '只能操作和查看检测任务',
		},
		username: {
			en: 'Username',
			cn: '用户名',
		},
		lastActive: {
			en: 'Last Active',
			cn: '最后活动',
		},
		role: {
			en: 'Role',
			cn: '角色',
		},
		viewChangePwd: {
			en: 'View/Change Password',
			cn: '查看/更改密码',
		},
		programmer: {
			en: 'Programmer',
			cn: '编程人员',
		},
		operator: {
			en: 'Operator',
			cn: '操作员',
		},
		addMember: {
			en: 'Add member',
			cn: '添加成员',
		},
		onlyAdminCanChange: {
			en: '* Only Admin can change password for the member. Members will not be able to change their password.',
			cn: '* 只有管理员可以更改成员的密码。成员将无法更改密码。',
		},
		newProgrammer: {
			en: 'New Programmer',
			cn: '新编程人员',
		},
		newOperator: {
			en: 'New Operator',
			cn: '新操作员',
		},
		password: {
			en: 'Password',
			cn: '密码',
		},
		passwordMustContain: {
			en: 'Password must contain:',
			cn: '密码必须包含：',
		},
		atLeast8: {
			en: 'At least 8 characters',
			cn: '至少8个字符',
		},
		atLeastOneLetter: {
			en: 'At least one letter',
			cn: '至少一个字母',
		},
		atLeastOneNumber: {
			en: 'At least one number',
			cn: '至少一个数字',
		},
		createAccount: {
			en: 'Create Account',
			cn: '创建账户',
		},
	},
	lineItemName: {
		text_verification: {
			en: 'Text Verification',
			cn: '文本验证',
		},
		mounting_inspection_2d: {
			en: 'Mounting Inspection (2D)',
			cn: '本体检测 (2D)',
		},
		mounting_inspection_3d: {
			en: 'Mounting Inspection (3D)',
			cn: '本体检测 (3D)',
		},
		lead_inspection_2d: {
			en: 'Lead Inspection (2D)',
			cn: 'IC引脚检测 (2D)',
		},
		lead_inspection_3d: {
			en: 'Lead Inspection (3D)',
			cn: 'IC引脚检测 (3D)',
		},
                solder_inspection: {
                        en: 'Solder Inspection',
                        cn: '焊锡检测',
                },
                solder_inspection_2d: {
                        en: 'Solder Inspection (2D)',
                        cn: '焊锡检测 (2D)',
                },
    barcode_scanner: {
      en: 'Barcode Scanner',
      cn: '条形码扫描',
    },
		lead_inspection_2d_v2: {
			en: 'Lead Inspection V2 (2D)',
			cn: 'IC引脚检测V2 (2D)',
		},
	},
	agentParamDesc: {
		mounting_inspection_3d: {
			check_tombstone: {
				en: 'Flag to return error type when lifted component is detected.',
				cn: '当检测到抬起的元件时，是否返回错误类型。',
			},
		},
		fuzzy_mode: {
			en: 'When enabled, text with similar shape will be considered as correct.',
			cn: '启用模糊模式后，形状相似的文本将被视为正确。',
		},
	},
	agentParamName: {
		text_verification: {
			expected_text: {
				en: 'Expected Text',
				cn: '期望文本',
			},
			enable_rotation: {
				en: 'Enable Rotation',
				cn: '启用旋转',
			},
			fuzzy_mode: {
				en: 'Blur Mode',
				cn: '模糊模式',
			},
		},
		mounting_inspection_2d: {
			mask_roi: {
				en: 'Enable Mask',
				cn: '启用遮罩',
			},
			threshold: {
				cn: '显著缺陷检测',
				en: 'Significant Defect Check',
			},
			defect_check_threshold: {
				cn: '细微缺陷检测',
				en: 'Subtle Defect Check',
			},
			polarity_roi: {
				en: 'Polarity ROI',
				cn: '极性ROI',
			},
			polarity_check_threshold: {
				en: 'Polarity Check',
				cn: '极性检测',
			},
			ext_left: {
				en: 'Left',
				cn: '左',
			},
		},
		mounting_inspection_3d: {
			extended_roi: {
				en: 'Extended ROI',
				cn: '扩展ROI',
			},
			ext_left: {},
			ext_right: {},
			ext_top: {},
			ext_bottom: {},
			check_tombstone: {
				en: 'Check Tombstone',
				cn: '检查立碑',
			},
			height_offset: {},
			rotation_offset: {},
			x_offset: {
				en: 'X Offset (mm)',
				cn: 'X偏移 (mm)',
			},
			y_offset: {
				en: 'Y Offset (mm)',
				cn: 'Y偏移 (mm)',
			},
			max_slope: {
				en: 'Max Slope',
				cn: '最大坡度',
			},
			height_range: {
				en: 'Height Range (mm)',
				cn: '高度范围 (mm)',
			},
			rotation_offset: {
				en: 'Rotation Offset (°)',
				cn: '旋转偏移 (°)',
			},
			background_roi1: {
				en: 'Background ROI 1',
				cn: '背景ROI 1',
			},
			background_roi2: {
				en: 'Background ROI 2',
				cn: '背景ROI 2',
			},
		},
		lead_inspection_2d: {
			lead_angle: {
				en: 'Lead Angle',
				cn: '引脚角度',
			},
			bridge_threshold: {
				en: 'Bridge Threshold',
				cn: '桥接阈值',
			},
			bridge_width_mm: {
				en: 'Bridge Width (mm)',
				cn: '桥接宽度 (mm)',
			},
			lead_count: {
				en: 'Lead Count',
				cn: '引脚数量',
			},
			lead_threshold: {
				en: 'Lead Threshold',
				cn: '引脚阈值',
			},
			lead_width_mm: {
				en: 'Lead Width (mm)',
				cn: '引脚宽度 (mm)',
			},
			enable_visualization: {
				en: 'Enable Visualization',
				cn: '启用可视化',
			},
		},
		lead_inspection_2d_v2: {
			solder123: {
				cn: '色块比例范围',
				en: 'Solder Color Range',
			},
			solder123Desc: {
				cn: '色块比例范围与检测点的颜色范围匹配度阈值',
				en: 'Threshold for matching the color range of the solder block with the inspection point',
			},
			colorRange: {
				en: 'Color Range Type',
				cn: '颜色范围类型',
			},
			ext_top: {
				en: 'Extended Top Length (pixel)',
				cn: '扩展顶部长度 (像素)',
			},
			ext_bot: {
				en: 'Extended Bottom Length (pixel)',
				cn: '扩展底部长度 (像素)',
			},
			tipColorRange: {
				en: 'Tip Color Range',
				cn: '引脚尖端颜色范围',
			},
			padColorRange: {
				en: 'Pad Color Range',
				cn: '焊盘颜色范围',
			},
			solderColorRange: {
				en: 'Solder Color Range',
				cn: '焊料颜色范围',
			},
			lead_angle: {
				en: 'Lead Angle',
				cn: '引脚角度',
			},
			bridge_threshold: {
				en: 'Bridge Threshold',
				cn: '桥接阈值',
			},
			bridge_width_mm: {
				en: 'Bridge Width (mm)',
				cn: '桥接宽度 (mm)',
			},
			lead_count: {
				en: 'Lead Count',
				cn: '引脚数量',
			},
			lead_threshold: {
				en: 'Lead Threshold',
				cn: '引脚阈值',
			},
			lead_width_mm: {
				en: 'Lead Width (mm)',
				cn: '引脚宽度 (mm)',
			},
			enable_visualization: {
				en: 'Enable Visualization',
				cn: '启用可视化',
			},
			solder_mean_threshold: {
				en: 'Solder Mean Threshold',
				cn: '焊料平均阈值',
			},
			solder_valid_ratio_range: {
				en: 'Solder Valid Ratio Range',
				cn: '焊料有效比例范围',
			},
                        solder_neighbor_threshold: {
                                en: 'Solder Neighbor Threshold',
                                cn: '焊料邻域阈值',
                        },
                        solder_valid_ratio_list: {
                                en: 'Solder Valid Ratio List',
                                cn: '焊料有效比例列表',
                        },
                        pad_valid_ratio_list: {
                                en: 'Pad Valid Ratio List',
                                cn: '焊盘有效比例列表',
                        },
                        tip_valid_ratio_list: {
                                en: 'Tip Valid Ratio List',
                                cn: '引脚尖端有效比例列表',
                        },
                        lifted_lead_solder_mean_threshold: {
                                en: 'Lifted Lead Solder Mean Threshold',
                                cn: '抬起引脚焊料平均阈值',
                        },
			lifted_lead_tip_valid_ratio_range: {
				en: 'Lifted Lead Tip Valid Ratio Range',
				cn: '抬起引脚尖端有效比例范围',
			},
			lifted_lead_tip_mean_lower_threshold: {
				en: 'Lifted Lead Tip Mean Lower Threshold',
				cn: '抬起引脚尖端平均下限阈值',
			},
			lifted_lead_tip_mean_upper_threshold: {
				en: 'Lifted Lead Tip Mean Upper Threshold',
				cn: '抬起引脚尖端平均上限阈值',
			},
			lifted_lead_min_solder_pad_difference: {
				en: 'Lifted Lead Min Solder Pad Difference',
				cn: '抬起引脚最小焊盘差异',
			},
			lifted_lead_solder_neighbor_threshold: {
				en: 'Lifted Lead Solder Neighbor Threshold',
				cn: '抬起引脚焊料邻域阈值',
			},
			lifted_lead_pad_mean_threshold: {
				en: 'Lifted Lead Pad Mean Threshold',
				cn: '抬起引脚焊盘平均阈值',
			},
		},
		lead_inspection_3d: {
			ext_top: {
				en: 'Extended Length (mm)',
				cn: '扩展长度 (mm)',
			},
			lead_angle: {
				en: 'Lead Angle',
				cn: '引脚角度',
			},
			bridge_threshold: {
				en: 'Bridge Threshold',
				cn: '桥接阈值',
			},
			bridge_width_mm: {
				en: 'Bridge Width (mm)',
				cn: '桥接宽度 (mm)',
			},
			lead_count: {
				en: 'Lead Count',
				cn: '引脚数量',
			},
			lead_threshold: {
				en: 'Lead Threshold',
				cn: '引脚阈值',
			},
			lead_width_mm: {
				en: 'Lead Width (mm)',
				cn: '引脚宽度 (mm)',
			},
			ext_bot: {
				en: 'Extended Bottom Length',
				cn: '扩展底部长度',
			},
			height_range: {
				en: 'Height Range',
				cn: '高度范围',
			},
			max_height_std: {
				en: 'Max Height Standard Deviation (mm)',
				cn: '最大高度标准差 (mm)',
			},
			min_height_mean: {
				en: 'Min Height Mean (mm)',
				cn: '最小高度均值 (mm)',
			},
		},
                solder_inspection: {
                        fillet_volume_ratio: {
                                en: 'Fillet Volume Ratio',
                                cn: '焊点体积比率',
                        },
			solder_angle: {
				en: 'Solder Angle',
				cn: '焊点角度',
			},
			component_tip_offset: {
				en: 'Component Tip Offset',
				cn: '元件尖端偏移',
			},
			fillet_lower_threshold: {
				en: 'Fillet Lower Threshold (%)',
				cn: '焊点下限阈值 (%)',
			},
			fillet_open_threshold: {
				en: 'Fillet Open Threshold (%)',
				cn: '焊点开放阈值 (%)',
			},
			fillet_upper_threshold: {
				en: 'Fillet Upper Threshold (%)',
				cn: '焊点上限阈值 (%)',
			},
			ideal_fillet_height_rate: {
				en: 'Ideal Fillet Height Rate',
				cn: '理想焊点高度比率',
			},
			max_component_angle: {
				en: 'Max Component Angle',
				cn: '最大元件角度',
			},
			max_pcb_angle: {
				en: 'Max PCB Angle',
				cn: '最大PCB角度',
			},
			min_fillet_height_rate: {
				en: 'Min Fillet Height Rate',
				cn: '最小焊点高度比率',
			},
			profile_mode: {
				en: 'Profile Mode',
				cn: '轮廓模式',
			},
			solder_direction: {
				en: 'Solder Direction',
				cn: '焊料方向',
			},
			solder_range: {
				en: 'Solder Range',
				cn: '焊料范围',
			},
			extended_roi: {
				en: 'Extended ROI',
				cn: '扩展ROI',
			},
                        profile_roi: {
                                en: 'Profile ROI',
                                cn: '轮廓ROI',
                        },
                },
                solder_inspection_2d: {
									enable_visualization: {
										en: 'Enable Visualization',
										cn: '启用可视化',
                        },
                        center_hue: {
                                en: 'Center Hue',
                                cn: '中心色调',
                        },
                        center_sat: {
                                en: 'Center Saturation',
                                cn: '中心饱和度',
                        },
                        start_hue: {
                                en: 'Start Hue',
                                cn: '起始色调',
                        },
                        end_hue: {
                                en: 'End Hue',
                                cn: '终止色调',
                        },
                        start_val: {
                                en: 'Start Value',
                                cn: '起始明度',
                        },
                        end_val: {
                                en: 'End Value',
                                cn: '终止明度',
                        },
                        valid_ratio_ranges: {
                                en: 'Valid Ratio Ranges',
                                cn: '有效比例范围',
                        },
                        valid_ratio_list: {
                                en: 'Valid Ratio List',
                                cn: '有效比例列表',
                        },
                },
        },
	productDefine: {
		common: {
			en: 'Common',
			cn: '通用',
		},
		solder: {
			en: 'Solder',
			cn: '焊料',
		},
		pad: {
			en: 'Pad',
			cn: '焊盘',
		},
		tip: {
			en: 'Tip',
			cn: '焊珠',
		},
		other: {
			en: 'Other',
			cn: '其他',
		},
		markerPointInfoPointA: {
			en: 'Marking the board on the top left',
			cn: '在左上角标记电路板',
		},
		markerPointInfoPointB: {
			en: 'Marking the board on the top right. Please ensure that the board is aligned with the top left board and the marker point is in the same position as the top left marker point.',
			cn: '在右上角标记电路板。请确保电路板与左上角电路板水平方向对齐，并且标记点与左上角标记点处于相同位置。',
		},
		markerPointInfoPointC: {
			en: 'Marking the board on the bottom left. Please ensure that the board is aligned with the top left board and the marker point is in the same position as the top left marker point.',
			cn: '在左下角标记电路板。请确保电路板与左上角电路板垂直方向对齐，并且标记点与左上角标记点处于相同位置。',
		},
		markShapeType: {
			en: 'Marker Shape Type',
			cn: '标记形状类型',
		},
		circleMarker: {
			en: 'Circle Marker',
			cn: '圆形标记',
		},
		rectangleMarker: {
			en: 'Rectangle Marker',
			cn: '矩形标记',
		},
		templateMarker: {
			en: 'Template Marker',
			cn: '模板标记',
		},
		reevaluationPass: {
			en: 'Re-evaluation Pass',
			cn: '复评通过',
		},
		componentHealthy: {
			en: 'Component is healthy',
			cn: '元件是否可信',
		},
		componentHealthDesc: {
			en: `Component is healthy: whether a (generated)component's setting output has a high confidence score or this component is confirmed to be healthy by user.`,
			cn: `元件可信：模型生成的元件设置置信度高，或用户确认此元件为可信。`,
		},
		lowConfidenceComponentsTitle: {
			en: 'Low Confidence Components Detected',
			cn: '检测到生成的元件中出现低置信度元件',
		},
		lowConfidenceComponentsMessage: {
			en: 'Please verify that the generated components are correct by clicking the question mark icon in the top right corner of each component.',
			cn: '请点击元件右上角的问号图标，确认生成的元件是否正确。',
		},
		capture2D: {
			en: 'Capture 2D',
			cn: '拍摄2D',
		},
		retrainConfirmation: {
			en: 'Retrain Confirmation',
			cn: '训练确认',
		},
		autoGenerateAgentParams: {
			en: 'Generate Params',
			cn: '自动生成检测参数',
		},
		pleaseSelectFeatureTypesAgentParams: {
			en: `Please select feature type(s)' agent parameters that should be automatically replaced.`,
			cn: `请选择需要自动替换的子元件类型的Agent参数。`,
		},
		confirmComponentInfo: {
			en: 'Confirm All Component Info',
			cn: '确认所有元件信息',
		},
		flipX: {
			en: 'Flip X',
			cn: '水平翻转',
		},
		flipY: {
			en: 'Flip Y',
			cn: '垂直翻转',
		},
                defineSelectionRoi: {
                        en: 'Define Selection ROI',
                        cn: '定义选择ROI',
                },
                redefineSelectionRoi: {
                        en: 'Re-define Selection ROI',
                        cn: '重新定义选择ROI',
                },
                arrayBoard: {
                        en: 'Array Board',
                        cn: '拼版',
                },
		useSmartArrayTool: {
			en: 'Use Smart Array Tool',
			cn: '使用智能拼板工具',
		},
		createArray: {
			en: 'Create Array',
			cn: '创建拼板',
		},
		previewArray: {
			en: 'Preview Array',
			cn: '预览拼板',
		},
		pointATopLeftUnit: {
			en: 'Point A: Top Left Unit',
			cn: '点A：左上角单元',
		},
		pointBTopRightUnit: {
			en: 'Point B: Top Right Unit',
			cn: '点B：右上角单元',
		},
		pointCBottomLeftUnit: {
			en: 'Point C: Bottom Left Unit',
			cn: '点C：左下角单元',
		},
		defineMarkerPointsOfThree: {
			en: 'Define marker points of 3 identical units',
			cn: '定义3个相同单元的标记点',
		},
		theMarkerPointsOfTheUnit: {
			en: 'The marker points of the unit will be used to calculate the position and alignment of the array.',
			cn: '单元的标记点将用于计算拼板的位置和对齐。',
		},
		gridType: {
			en: 'Grid Type',
			cn: '网格类型',
		},
		createArrayUnit: {
			en: 'Create Array Unit',
			cn: '创建拼板单元',
		},
		createInitialArrayUnit: {
			en: 'Create Initial Array Unit',
			cn: '创建初始拼板单元',
		},
		selectAllRelatedComponent: {
			en: 'Select all related components, and click “Create Array Unit” to group them as a unit. Once you click the button, you can start defining the array unit in the panel on the right.',
			cn: '选择所有相关元件，然后点击“创建拼板单元”将它们分组为一个单元。当您点击按钮后，即可开始在右边的面板中定义拼板单元。',
		},
		smartPCBArray: {
			en: 'Smart PCB Array',
			cn: '智能PCB拼板',
		},
		arrayGroup: {
			en: 'Array Group',
			cn: '拼板分组',
		},
		pcbaArray: {
			en: 'PCB Array',
			cn: 'PCB拼板',
		},
		startAutoProgramming: {
			en: 'Start Auto Programming',
			cn: '开始自动编程',
		},
		configAutoProgram: {
			en: 'Configure Auto Program',
			cn: '配置自动编程',
		},
		redefineInpsectionRegion: {
			en: 'Re-define Inspection Region',
			cn: '重新定义检测区域',
		},
		autoAdjustRois: {
			en: 'Auto-adjust ROIs',
			cn: '自动调整ROI',
		},
		keepInternalRoisPosition: {
			en: 'Keep internal ROIs position',
			cn: '保持内部ROI位置',
		},
		onComponentMove: {
			en: 'On Component Move',
			cn: '元件移动时',
		},
		partNoWithEmptyPackageNo: {
			en: 'Part No with empty package no',
			cn: '无封装料号',
		},
		componentWithEmptyPartNo: {
			en: 'Component with empty part no',
			cn: '无料号元件',
		},
		linkPackage: {
			en: 'Link Package',
			cn: '连接封装',
		},
		linkPart: {
			en: 'Link Part',
			cn: '连接料号',
		},
		partInfo: {
			en: 'Part Info',
			cn: '部件信息',
		},
		packageInfo: {
			en: 'Package Info',
			cn: '封装信息',
		},
		predictedErrorType: {
			en: 'Predicted Error Type',
			cn: '预测错误类型',
		},
		rotate180: {
			en: 'Rotate 180°',
			cn: '旋转180°',
		},
		rotate90Left: {
			en: 'Rotate 90° Left',
			cn: '向左旋转90°',
		},
		rotate90Right: {
			en: 'Rotate 90° Right',
			cn: '向右旋转90°',
		},
		rotateClockwise: {
			en: 'Rotate Clockwise',
			cn: '顺时针旋转',
		},
		rotateCounterClockwise: {
			en: 'Rotate Counter Clockwise',
			cn: '逆时针旋转',
		},
		ungroupFeature: {
			en: 'Ungroup Features',
			cn: '取消分组子元件',
		},
    addBarcodeFeature: {
      en: 'Add Barcode Feature',
      cn: '添加条形码检测',
    },
		predictCenter: {
			en: 'Predict Center',
			cn: '预测中心',
		},
		stopContinuous: {
			en: 'Stop Continuous',
			cn: '停止连续',
		},
		removeComponentWithFeature: {
			en: 'Remove Component',
			cn: '删除元件',
		},
                copyComponent: {
                        en: 'Copy Component',
                        cn: '复制元件',
                },
                addToPrivateLibrary: {
                        en: 'Add to Private Library',
                        cn: '添加到私有库',
                },
                saveToPrivateLibrary: {
                        en: 'Save to Private Library',
                        cn: '保存到私有库',
                },
                copyFeatureIntoComponent: {
                        en: 'Duplicate',
                        cn: '复制子元件',
                },
		locateFeature: {
			en: 'Locate Feature',
			cn: '定位子元件',
		},
		locateComponent: {
			en: 'Locate Component',
			cn: '定位元件',
		},
		addComponent: {
			en: 'Add Component',
			cn: '添加元件',
		},
		crop3DView: {
			en: 'Crop 3D View',
			cn: '裁剪3D视图',
		},
		select: {
			en: 'Select',
			cn: '选择',
		},
		panZoom: {
			en: 'Pan/Zoom',
			cn: '平移/缩放',
		},
		providedFeedbackCount: {
			en: 'Provided Feedback Count',
			cn: '提供反馈数量',
		},
		ungroupedFeature: {
			en: 'Ungrouped Feature',
			cn: '未分组子元件',
		},
		setAsGolden: {
			en: 'Set as Golden',
			cn: '设置为基准',
		},
		items: {
			en: 'Items',
			cn: '项目',
		},
		taskID: {
			en: 'Task ID',
			cn: '任务ID',
		},
		product: {
			en: 'Product',
			cn: '产品',
		},
		sn: {
			en: 'S/N',
			cn: '序列号',
		},
		hasFeedbackOnly: {
			en: 'Show items with feedback only',
			cn: '仅显示有反馈的项目',
		},
		noFeedbackOnly: {
			en: 'Show items without feedback only',
			cn: '仅显示没有反馈的项目',
		},
		passedOnly: {
			en: 'Show predicted as passed Only',
			cn: '仅显示预测合格',
		},
		failedOnly: {
			en: 'Show predicted as failed Only',
			cn: '仅显示预测不合格',
		},
		showAll: {
			en: 'Show All',
			cn: '显示全部',
		},
		allItems: {
			en: 'All Items',
			cn: '所有项目',
		},
		trainingSet: {
			en: 'Training Set',
			cn: '训练集',
		},
		reevaluateAll: {
			en: 'Reevaluate All',
			cn: '重新评估全部',
		},
                searchComponent: {
                        en: 'Search component by designator, package number or part number',
                        cn: '通过位号、封装或料号搜索元件',
                },
                healthy: {
                        en: 'Healthy',
                        cn: '健康',
                },
                unhealthy: {
                        en: 'Unhealthy',
                        cn: '不健康',
                },
                reevaluationPass: {
                        en: 'Reevaluation Pass',
                        cn: '复评通过',
                },
                reevaluationFail: {
                        en: 'Reevaluation Fail',
                        cn: '复评失败',
                },
                retrainModel: {
                        en: 'Train',
                        cn: '训练',
                },
		addBodyFeature: {
			en: 'Add Body Feature',
			cn: '添加本体检测',
		},
		addLeadFeature: {
			en: 'Add Lead Feature',
			cn: '添加IC引脚检测',
		},
		addSolderFeature: {
			en: 'Add Solder Feature',
			cn: '添加焊锡检测',
		},
		addTextFeature: {
			en: 'Add Text Feature',
			cn: '添加文本检测',
		},
		newComponent: {
			en: 'Create New Component',
			cn: '创建新元件',
		},
		groupSelectedFeatures: {
			en: 'Group selection as new component',
			cn: '将选中的子元件分组为新元件',
		},
		newFeaturePleaseEnterTheDesignator: {
			en: 'New feature, please enter the designator',
			cn: '新子元件，请输入标识',
		},
		setPolarityRoi: {
			en: 'Set Polarity ROI',
			cn: '设置极性ROI',
		},
		inspectionCount: {
			en: 'Inspection Count',
			cn: '检测次数',
		},
		feedback: {
			en: 'Feedback',
			cn: '反馈',
		},
		passAll: {
			en: 'Pass All',
			cn: '全部合格',
		},
		failAll: {
			en: 'Fail All',
			cn: '全部不合格',
		},
		defectTotal: {
			en: 'Defect Total',
			cn: '缺陷总数',
		},
		failBoard: {
			en: 'Fail Board',
			cn: '不合格板',
		},
		exitDrawMode: {
			en: 'Exit Draw Mode',
			cn: '退出绘制模式',
		},
		deleteFeature: {
			en: 'Delete Feature',
			cn: '删除子元件',
		},
		deleteComponent: {
			en: 'Delete Component',
			cn: '删除元件',
		},
		findTemplateFromLibrary: {
			en: 'Find template from library',
			cn: '从库中查找模板',
		},
		replaceFeaturesWithTemplateFromLibrary: {
			en: 'Replace with template from library',
			cn: '用库中的模板替换',
		},
		refDesignator: {
			en: 'Ref./Designator',
			cn: '丝印/位号/标识',
		},
		pleaseCheckIfPackage: {
			en: 'Please check if package and part number are correct. Draw missing ROIs, or add ROI template from library.',
			cn: '请检查包装和料号是否正确。绘制缺失的ROI，或从库中添加ROI模板。',
		},
		componentInfo: {
			en: 'Component Info',
			cn: '元件信息',
		},
		missingROI: {
			en: 'Missing feature',
			cn: '缺少子元件',
		},
		removeFeatureFromComponent: {
			en: 'Ungroup from component',
			cn: '从元件中移除',
		},
		ignoreThisCol: {
			en: 'Ignore this column',
			cn: '忽略此列',
		},
		configureROIMatchSourceAndDefaultEnabledInspection: {
			en: `Configure ROI's match source, and default enabled inspection`,
			cn: '配置ROI的匹配源和默认启用的检测',
		},
		bothPrivateAndPublic: {
			en: 'Both private and public',
			cn: '私有和公共',
		},
		findAndMatch: {
			en: 'Find match in component library:',
			cn: '在元件库中查找匹配：',
		},
		configureROIs: {
			en: 'Configure ROIs',
			cn: '配置ROI',
		},
		finishAndGenerateROI: {
			en: 'Finish and Generate ROIs',
			cn: '完成并生成ROI',
		},
		selectInspectionTypes: {
			en: 'Select default inspection types for detected ROIs:',
			cn: '选择检测ROI的默认检测类型：',
		},
		continuous: {
			en: 'Continuous',
			cn: '连续',
		},
		inspect: {
			en: 'Inspect',
			cn: '检测',
		},
		stop: {
			en: 'Stop',
			cn: '停止',
		},
		PCBIn: {
			en: 'PCB In',
			cn: '进板',
		},
		PCBEject: {
			en: 'PCB Eject',
			cn: '出板',
		},
		clampOn: {
			en: 'Clamp On',
			cn: '夹紧',
		},
		clampOff: {
			en: 'Clamp Off',
			cn: '松开',
		},
		passThru: {
			en: 'Pass Thru',
			cn: '通过',
		},
		reset: {
			en: 'Reset',
			cn: '重置',
		},
		recipe: {
			en: 'Recipe',
			cn: '设置',
		},
		components: {
			en: 'Component',
			cn: '元件',
		},
		PCBDetail: {
			en: 'PCB Detail',
			cn: 'PCB详情',
		},
		conveyorSetup: {
			en: 'Conveyor Setup',
			cn: '传送带设置',
		},
		PCBDimension: {
			en: 'PCB Dimension',
			cn: 'PCB尺寸',
		},
		fullPCBCapture: {
			en: 'Full PCB Capture',
			cn: '全PCB拍摄',
		},
		PCBInformation: {
			en: 'PCB Information',
			cn: 'PCB信息',
		},
		productName: {
			en: 'Product Name',
			cn: '产品名称',
		},
		specsForInspection: {
			en: 'Specs for Inspection',
			cn: '检测参数',
		},
		conveyorWidth: {
			en: 'Conveyor Width',
			cn: '传送带宽度',
		},
		saveAndContinue: {
			en: 'Save and Continue',
			cn: '保存并继续',
		},
		releaseConveyor: {
			en: 'Release Conveyor',
			cn: '释放传送带',
		},
		lockConveyorForThisTask: {
			en: 'Lock Conveyor for this Task',
			cn: '锁定传送带',
		},
		conveyorSetupDescPart1: {
			en: 'Pick a conveyor and move to width, then put a PCB in by hand, or use PCB In',
			cn: '选择传送带并移动到宽度，然后手动放入PCB，或使用PCB进入',
		},
		conveyorSetupDescPart2: {
			en: 'to place it on conveyor.',
			cn: '将其放在传送带上。',
		},
		selectAConveyor: {
			en: 'Select a Conveyor',
			cn: '选择一个传送带',
		},
		refreshStatus: {
			en: 'Refresh Status',
			cn: '刷新状态',
		},
		front: {
			en: 'Front',
			cn: '前',
		},
		back: {
			en: 'Back',
			cn: '后',
		},
		available: {
			en: 'Available',
			cn: '可用',
		},
		inUse: {
			en: 'In Use',
			cn: '使用中',
		},
		setConveyorWidth: {
			en: 'Set Conveyor Width: (Limit: 50 - 350 mm)',
			cn: '设置传送带宽度：（限制：50 - 350 mm）',
		},
		moveToWidth: {
			en: 'Move to width',
			cn: '移动到宽度',
		},
		currentConveyorWidth: {
			en: 'Current Conveyor Width',
			cn: '当前传送带宽度',
		},
		measurePCBDimension: {
			en: 'Measure PCB Dimension',
			cn: '测量PCB尺寸',
		},
		instructions: {
			en: 'Instructions',
			cn: '说明',
		},
		instructionStep1: {
			en: 'Step 1: Move the camera to the bottom-left of the PCB and capture an image.',
			cn: '步骤1：将相机移动到PCB的左下角并拍摄一张图像。',
		},
		instructionStep2: {
			en: 'Step 2: Select the bottom-left pixel of the PCB on the image.',
			cn: '步骤2：在图像上选择PCB的左下角像素。',
		},
		instructionStep3: {
			en: 'Step 3: Repeat for the top-right corner.',
			cn: '步骤3：在右上角重复此操作。',
		},
		instructionStep4: {
			en: 'Step 4: Calculate dimensions.',
			cn: '步骤4：计算尺寸。',
		},
		inputCameraPosition: {
			en: 'Input Camera Position',
			cn: '输入相机位置',
		},
		moveCamera: {
			en: 'Move Camera',
			cn: '移动相机',
		},
		capture2DImage: {
			en: 'Capture 2D Image',
			cn: '拍摄2D图像',
		},
		PCBDimension: {
			en: 'PCB Dimensions',
			cn: 'PCB尺寸',
		},
		calcualateDimension: {
			en: 'Calculate Dimension',
			cn: '计算尺寸',
		},
		bottomLeftPixel: {
			en: 'Bottom-left pixel',
			cn: '左下角像素',
		},
		topRightPixel: {
			en: 'Top-right pixel',
			cn: '右上角像素',
		},
		selectPixel: {
			en: 'Select Pixel',
			cn: '选择像素',
		},
		capture2dAnd3d: {
			en: 'Capture 2D and 3D',
			cn: '拍摄2D和3D',
		},
		performAFullCapture: {
			en: 'Perform a full capture before adjusting the 2D and 3D settings.',
			cn: '在调整2D和3D设置之前执行完整拍摄。',
		},
		twoDCapture: {
			en: '2D Capture',
			cn: '2D拍摄',
		},
		threeDCapture: {
			en: '3D Capture',
			cn: '3D拍摄',
		},
		exposureTime: {
			en: 'Exposure Time',
			cn: '曝光时间',
		},
		gain: {
			en: 'Gain',
			cn: '增益',
		},
		resetToDefault: {
			en: 'Reset to Default',
			cn: '重置为默认',
		},
		quick2DCapture: {
			en: 'Quick 2D Capture',
			cn: '快速2D拍摄',
		},
		frameSetting: {
			en: 'Frame Setting',
			cn: '帧设置',
		},
		exposureStop: {
			en: 'Exposure Stop',
			cn: '曝光档位',
		},
		brightness: {
			en: 'Brightness',
			cn: '亮度',
		},
		gain: {
			en: 'Gain',
			cn: '增益',
		},
		pointCloudFilter: {
			en: 'Point Cloud Filter',
			cn: '点云过滤器',
		},
		outlier: {
			en: 'Outlier',
			cn: '异常值',
		},
		smoothing: {
			en: 'Smoothing',
			cn: '平滑',
		},
		contrast: {
			en: 'Contrast',
			cn: '对比度',
		},
		correction: {
			en: 'Correction',
			cn: '校正',
		},
		outlierRemoval: {
			en: 'Outlier Removal (Advanced)',
			cn: '异常值去除 (高级)',
		},
		outlierThresholdLevel: {
			en: 'Outlier Threshold Level',
			cn: '异常值阈值级别',
		},
		faceNormalFilter: {
			en: 'Face Normal Filter',
			cn: '法向量过滤器',
		},
		clusterFilter: {
			en: 'Cluster Filter',
			cn: '移除小型离散区域',
		},
		strength: {
			en: 'Strength',
			cn: '强度',
		},
		neighborDistance: {
			en: 'Neighbor Distance',
			cn: '邻居距离',
		},
		smoothingStrength: {
			en: 'Smoothing Strength',
			cn: '平滑强度',
		},
		smoothingSettings: {
			en: 'Smoothing Settings: (advanced)',
			cn: '平滑设置: (高级)',
		},
		gaussianFilter: {
			en: 'Gaussian Filter',
			cn: '高斯滤波器',
		},
		medianFilter: {
			en: 'Median Filter',
			cn: '中值滤波器',
		},
		smoothFilter: {
			en: 'Smooth Filter',
			cn: '平滑滤波器',
		},
		contrastSettings: {
			en: 'Contrast Settings: (Advanced)',
			cn: '对比度设置: (高级)',
		},
		intensityThreshold: {
			en: 'Intensity Threshold',
			cn: '强度阈值',
		},
		removeOverexposeRegion: {
			en: 'Remove Overexpose Region',
			cn: '移除过曝光区域',
		},
		removeLowQualityRegion: {
			en: 'Remove Low Quality Region',
			cn: '移除低质量区域',
		},
		fillGaps: {
			en: 'Fill Gaps',
			cn: '填补缺口',
		},
		holeSize: {
			en: 'Hole Size',
			cn: '孔尺寸',
		},
		depthDifference: {
			en: 'Depth Difference',
			cn: '深度差异',
		},
		contrastDistortion: {
			en: 'Contrast Distortion',
			cn: '对比度失真',
		},
		correctLevel: {
			en: 'Correct Level',
			cn: '校正级别',
		},
		removeLevel: {
			en: 'Remove Level',
			cn: '移除级别',
		},
		correct: {
			en: 'Correct',
			cn: '校正',
		},
		remove: {
			en: 'Remove',
			cn: '移除',
		},
		markAlignPCB: {
			en: 'Marker/ Align PCB',
			cn: '标记/对齐PCB',
		},
		templateEditor: {
			en: 'Template Editor',
			cn: '模板编辑器',
		},
		defineTwoMakers: {
			en: 'Define TWO markers to align PCB on the conveyor.',
			cn: '定义两个标记以在传送带上对齐PCB。',
		},
		marker: {
			en: 'Marker',
			cn: '标记',
		},
		locate: {
			en: 'Locate',
			cn: '定位',
		},
		refine: {
			en: 'Refine',
			cn: '调整',
		},
		drawROIAndDefine: {
			en: 'Draw ROI, and define inspection for the components.',
			cn: '绘制ROI，并定义元件的检测。',
		},
		aiDetectROI: {
			en: 'AI-detect ROI',
			cn: 'AI检测ROI',
		},
		generateRoi: {
			en: 'Generate ROI',
			cn: '生成ROI',
		},
		selectInspectionTypes: {
			en: 'Select inspection types for the detected ROI:',
			cn: '选择检测ROI的检测类型：',
		},
		body: {
			en: 'Body — Mounting',
			cn: '主体 — 安装',
		},
		ICLead: {
			en: 'IC Lead',
			cn: 'IC引脚',
		},
		OCR2D: {
			en: 'OCR (2D)',
			cn: 'OCR (2D)',
		},
		QR2D: {
			en: 'QR (2D)',
			cn: 'QR (2D)',
		},
		Text2D: {
			en: 'Text (2D)',
			cn: '文本 (2D)',
		},
		solder: {
			en: 'Solder',
			cn: '焊料',
		},
		trainingSet: {
			en: 'Training Set',
			cn: '训练集',
		},
		template: {
			en: 'Template',
			cn: '模板',
		},
		selectOrCreateANewComponent: {
			en: 'Select or create a new component.',
			cn: '选择或创建一个新元件。',
		},
		selectedComponent: {
			en: 'Selected Component',
			cn: '已选择元件',
		},
		conveyorRelatedReminder: {
			en: 'If connected to the conveyor, the system will auto-release the conveyor control once you leave the recipe page.',
			cn: '如果已连接传送带，系统将在您离开配置页面时自动释放传送带控制。',
		},
		setSpecs: {
			en: 'Set Specs',
			cn: '设置参数',
		},
		captured2DAnd3DData: {
			en: 'Captured 2D and 3D data',
			cn: '已拍摄的2D和3D数据',
		},
		toFinishUpdateTheFollowing: {
			en: 'To finish/update the following steps, please re-connect to a conveyor.',
			cn: '要完成/更新以下步骤，请重新连接到传送带。',
		},
		createComponent: {
			en: 'Create Component',
			cn: '创建元件',
		},
		duplicateRoi: {
			en: 'Duplicate ROI',
			cn: '复制ROI',
		},
		panelLength: {
			en: 'Panel Length (mm):',
			cn: '板长度 (mm):',
		},
		panelWidth: {
			en: 'Panel Width (mm):',
			cn: '板宽度 (mm):',
		},
		resetDimension: {
			en: 'Reset Dimension',
			cn: '重置尺寸',
		},
		editFullPCBCapture: {
			en: 'Edit Full PCB Capture',
			cn: '编辑全PCB拍摄',
		},
		editComponents: {
			en: 'Edit Components',
			cn: '编辑元件',
		},
		addFromLibrary: {
			en: 'Add from Library',
			cn: '从库中添加',
		},
		importCad: {
			en: 'Import CAD',
			cn: '导入CAD',
		},
		importPickAndPlaceFile: {
			en: 'Import component info file',
			cn: '导入元件信息文件',
		},
		parseFile: {
			en: 'Parse File',
			cn: '解析文件',
		},
		alignCoord: {
			en: 'Align Coordinate',
			cn: '对齐坐标',
		},
		configRois: {
			en: 'Config ROIs',
			cn: '配置ROI',
		},
		reupload: {
			en: 'Re-upload',
			cn: '重新上传',
		},
		fileEncoding: {
			en: 'File Encoding',
			cn: '文件编码',
		},
		autoDetect: {
			en: 'Auto Detect',
			cn: '自动检测',
		},
		encodingMaybeIncorrect: {
			en: 'Encoding may be incorrect, please try other encodings if you see garbled text',
			cn: '编码可能不正确，如果显示乱码请尝试其他编码',
		},
		encodingError: {
			en: 'Failed to read file encoding, please try other encodings',
			cn: '读取文件编码失败，请尝试其他编码',
		},
		previewTable: {
			en: 'Preview Table',
			cn: '预览表格',
		},
		delimiter: {
			en: 'Delimiter',
			cn: '分隔符',
		},
		firstRowIndex: {
			en: '1st Row Index',
			cn: '第一行索引',
		},
		lastRowIndex: {
			en: 'Last Row Index',
			cn: '最后一行索引',
		},
		mapData: {
			en: 'Map Data',
			cn: '映射数据',
		},
		assignColumn: {
			en: 'Assign column types on the table',
			cn: '在表格上分配列类型',
		},
		coordinateUnit: {
			en: 'Coordinate Unit',
			cn: '坐标单位',
		},
		useTheComponent: {
			en: 'Use the component info exported from SMT Machine (.csv, .tsv and .txt)',
			cn: '使用从SMT机器导出的元件信息文件 (.csv, .tsv 和 .txt)',
		},
		space: {
			en: 'Space',
			cn: '空格',
		},
		packageNo: {
			en: 'Package No.',
			cn: '封装',
		},
		partNo: {
			en: 'Part No.',
			cn: '料号',
		},
		centerX: {
			en: 'Center X',
			cn: '中心X坐标',
		},
		centerY: {
			en: 'Center Y',
			cn: '中心Y坐标',
		},
		selectFieldTypeForThisColumn: {
			en: 'Select field type for this column',
			cn: '为此列选择字段类型',
		},
		coordUnitMM: {
			en: 'mm',
			cn: '毫米',
		},
		coordUnitMIL: {
			en: 'mil',
			cn: '密尔',
		},
		botLayerCol: {
			en: 'Bot Layer Column',
			cn: '底层列',
		},
		rotationDegCol: {
			en: 'Rotation Degree Column',
			cn: '旋转角度',
		},
		layerCol: {
			en: 'Layer Column',
			cn: '层',
		},
		ignoreBotLayer: {
			en: 'Ignore Bottom Layer',
			cn: '忽略底层',
		},
		ignoreBotLayerIdentifier: {
			en: 'Ignore Bottom Layer Identifier',
			cn: '忽略底层标识符',
		},
		ignoreTopLayerIdentifier: {
			en: 'Ignore Top Layer Identifier',
			cn: '忽略顶层标识符',
		},
		moveAndAlign: {
			en: 'Move and align the centre coordinates of all components on the product.',
			cn: '移动并对齐产品上所有元件的中心坐标。',
		},
		searchCategory: {
			en: 'Search Category',
			cn: '搜索类别',
		},
		searchPackageNo: {
			en: 'Search Package No.',
			cn: '搜索封装',
		},
	},
	liveInspection: {
		arrayIndex: {
			en: 'Array Index',
			cn: '子版索引',
		},
		failureRatio: {
			en: 'Sub Board Component NG Ratio',
			cn: '子版元件NG率',
		},
		defectiveSubBoardFound: {
			en: 'Defective sub board found',
			cn: '检测到不合格子版',
		},
		numberOfDefectDetected: {
			en: 'Number of defect detected exceeds threshold for these array units.',
			cn: '检测到的缺陷数量超出阈值。',
		},
		subBoardDefectRatioThreshold: {
			en: 'Sub board defect ratio threshold',
			cn: '子版缺陷率阈值',
		},
		pass: {
			en: 'Pass',
			cn: '合格',
		},
		fail: {
			en: 'Fail',
			cn: '不合格',
		},
		inspecting: {
			en: 'Inspecting',
			cn: '检测中',
		},
		listening: {
			en: 'Listening',
			cn: '等待中',
		},
		stopped: {
			en: 'Stopped',
			cn: '已停止',
		},
		unknown: {
			en: 'Unknown Status',
			cn: '未知状态',
		},
		boardFPY: {
			en: 'Board FPY',
			cn: '主板通过率',
		},
		board: {
			en: 'Board',
			cn: '主板',
		},
		inspected: {
			en: 'Inspected',
			cn: '已检测',
		},
		pass: {
			en: 'Pass',
			cn: '合格',
		},
		fail: {
			en: 'Fail',
			cn: '不合格',
		},
	},
	featureTypes: {
		body: {
			en: 'Body',
			cn: '主体',
		},
		ICLead: {
			en: 'IC Lead',
			cn: 'IC引脚',
		},
		solder: {
			en: 'Solder',
			cn: '焊料',
		},
	},
	loader: {
		refetchingFeatures: {
			en: 'Refetching features...',
			cn: '正在重新获取子元件...',
		},
		autoGeneratingAgentParams: {
			en: 'Auto generating inspection params...',
			cn: '正在自动生成检测参数...',
		},
		autoProgramming: {
			en: 'Auto programming, this process may take up to 30 seconds...',
			cn: '自动编程中，此过程可能需要30秒...',
		},
		modelTraining: {
			en: 'Model training... Please wait.',
			cn: '模型训练中... 请稍等。',
		},
		singleInspectionProcessing: {
			en: 'Single inspection processing...',
			cn: '单个检测处理中...',
		},
		stopingInferenceSession: {
			en: 'Stoping inference task...',
			cn: '正在停止推理任务...',
		},
		loadingCroppedImage: {
			en: 'Loading cropped image...',
			cn: '加载裁剪图像中...',
		},
		cameraCapturing: {
			en: 'Camera capturing...',
			cn: '相机拍摄中...',
		},
		loadingCroppedPointCloud: {
			en: 'Loading cropped point cloud...',
			cn: '加载裁剪点云中...',
		},
		productRegister: {
			en: 'Registering product...',
			cn: '注册产品中...',
		},
		addingNewComponent: {
			en: 'Adding new component...',
			cn: '添加新元件中...',
		},
		updatingComponent: {
			en: 'Updating component...',
			cn: '更新元件中...',
		},
                updatingFeature: {
                        en: 'Updating feature...',
                        cn: '更新子元件中...',
                },
                reevaluatingAllExamples: {
                        en: 'Reevaluating all examples...',
                        cn: '重新评估全部示例中...',
                },
        },
        worklist: {
                displayAllInspectionResult: {
                        en: 'Display All Inspection Result',
			cn: '显示所有检测结果',
		},
		displayWorklist: {
			en: 'Display Worklist',
			cn: '显示工作列表',
		},
		allInspectionResult: {
			en: 'All Inspection Result',
			cn: '所有检测结果',
		},
		rescan: {
			en: 'Rescan',
			cn: '重新扫描',
		},
		deleteSession: {
			en: 'Delete Session',
			cn: '删除检测任务',
		},
		filterBySerialNumber: {
			en: 'Filter by Serial Number',
			cn: '按序列号过滤',
		},
		searchBySerialNo: {
			en: 'Search by Serial No.',
			cn: '按序列号搜索',
		},
		serialNo: {
			en: 'Serial No.',
			cn: '序列号',
		},
		provided: {
			en: 'Provided',
			cn: '已提供',
		},
		notProvided: {
			en: 'Not Provided',
			cn: '未提供',
		},
		goldenProductName: {
			en: 'Golden Product Name',
			cn: '基准产品名称',
		},
		date: {
			en: 'Date',
			cn: '日期',
		},
		totalComponent: {
			en: 'Total Components',
			cn: '总元件',
		},
		okngCounts: {
			en: 'OK/NG Counts',
			cn: '合格/不合格数量',
		},
		passFail: {
			en: 'Pass/Fail',
			cn: '合格/不合格',
		},
		feedback: {
			en: 'Feedback',
			cn: '反馈',
		},
		actions: {
			en: 'Actions',
			cn: '操作',
		},
		notProvided: {
			en: 'Not Provided',
			cn: '未提供',
		},
		review: {
			en: 'Review',
			cn: '查看',
		},
		fail: {
			en: 'Fail',
			cn: '不合格',
		},
		pass: {
			en: 'Pass',
			cn: '合格',
		},
		clearFilter: {
			en: 'Clear Filter',
			cn: '清除过滤器',
		},
		sessionGoldenProd: {
			en: 'Session Golden Product',
			cn: '检测基准产品',
		},
		totalProducts: {
			en: 'Total Products',
			cn: '总产品',
		},
		goodProducts: {
			en: 'Good Products',
			cn: '合格产品',
		},
		defectiveProducts: {
			en: 'Defective Products',
			cn: '不合格产品',
		},
		feedbackProvided: {
			en: 'Feedback Provided Only',
			cn: '仅显示反馈已提供',
		},
		onlyDisplayDefectiveItems: {
			en: 'Only display defective items',
			cn: '仅显示不合格项目',
		},
		filterByGoldenProduct: {
			en: 'Filter by Golden Product',
			cn: '按基准产品过滤',
		},
		worklist: {
			en: 'Worklist',
			cn: '工作列表',
		},
		taskId: {
			en: 'Task-ID',
			cn: '任务ID',
		},
		PCBType: {
			en: 'PCB Name',
			cn: 'PCB名称',
		},
		totalItems: {
			en: 'Total Items',
			cn: '总检测数',
		},
		itemsNG: {
			en: 'Items: NG',
			cn: '不合格数',
		},
		viewAllItems: {
			en: 'View All Items',
			cn: '查看所有项目',
		},
		viewAllInspectionRecords: {
			en: 'View All Inspection Records',
			cn: '查看所有检测记录',
		},
	},
	review: {
		referenceRange: {
			en: 'Reference Range',
			cn: '参考范围',
		},
		top: {
			en: 'Top',
			cn: '顶部',
		},
		front: {
			en: 'Front',
			cn: '前面',
		},
		back: {
			en: 'Back',
			cn: '后面',
		},
		left: {
			en: 'Left',
			cn: '左边',
		},
		right: {
			en: 'Right',
			cn: '右边',
		},
		toggleIpcCloudVisibility: {
			en: 'Toggle Inspected Cloud Visibility',
			cn: '切换检测点云可见性',
		},
		toggleGoldenCloudVisibility: {
			en: 'Toggle Golden Cloud Visibility',
			cn: '切换基准点云可见性',
		},
		sample: {
			en: 'Sample',
			cn: '样本',
		},
		golden: {
			en: 'Golden',
			cn: '基准',
		},
		display2DVisual: {
			en: 'Display 2D Visual',
			cn: '显示2D视图',
		},
		display3DVisual: {
			en: 'Display 3D Visual',
			cn: '显示3D视图',
		},
		displayColorMap: {
			en: 'Display Color Map',
			cn: '显示彩色图',
		},
		displayDepthMap: {
			en: 'Display Depth Map',
			cn: '显示深度图',
		},
		displayComparison: {
			en: 'Display Comparison View',
			cn: '显示比较视图',
		},
		displayOverlaid: {
			en: 'Display Overlaid View',
			cn: '显示叠加视图',
		},
		blurCharacter: {
			en: 'Blur Character',
			cn: '模糊字符',
		},
		ocrBlurTextDesc: {
			en: 'Characters highlighted in red are mismatched. (If fuzzy mode is enabled)Characters highlighted in blue have high text shape similarity.',
			cn: '红色高亮的字符表示文本不匹配，(如果启用了模糊模式)蓝色高亮的字符表示文本形状相似度高。',
		},
		validRatio: {
			en: 'Valid Ratio',
			cn: '有效比例',
		},
		displayHideInvalidPart: {
			en: 'Display/Hide Invalid Color Part',
			cn: '显示/隐藏无效颜色部分',
		},
		lead: {
			en: 'Lead',
			cn: '引脚',
		},
		colorRange: {
			en: 'Color Range',
			cn: '颜色范围',
		},
		setAsAlternative: {
			en: 'Set as Alternative',
			cn: '设置为替代料',
		},
		displayPointcloud: {
			en: 'Display Point Cloud',
			cn: '显示点云',
		},
		displayDepthMap: {
			en: 'Display Depth Map',
			cn: '显示深度图',
		},
		displayColorMap: {
			en: 'Display Color Map',
			cn: '显示彩色图',
		},
		backToWroklist: {
			en: 'Back to Worklist',
			cn: '返回工作列表',
		},
		checkerDesc: {
			en: 'Inspected as passed',
			cn: '检测为合格',
		},
		warningDesc: {
			en: 'Inspected as failed',
			cn: '检测为不合格',
		},
		thumbsUpDesc: {
			en: 'Good feedback provided',
			cn: '提供了合格的反馈',
		},
		thumbsDownDesc: {
			en: 'Bad feedback provided',
			cn: '提供了不合格的反馈',
		},
		backToLive: {
			en: 'Back to Live Inspection',
			cn: '返回实时检测',
		},
		defectFeedback: {
			en: 'Defect Feedback:',
			cn: '缺陷反馈:',
		},
		exportThisAgentResult: {
			en: 'Export this agent result',
			cn: '导出此Agent检测结果',
		},
		thisErrorTypeWasFeedbacked: {
			cn: '已反馈此错误类型',
			en: 'This error type was feedbacked',
		},
	},
	inferenceReferenceComparison: {
		inferenceResult: {
			en: 'Inference Result',
			cn: '检测结果',
		},
		referenceValue: {
			en: 'Reference Value',
			cn: '基准值',
		},
	},
	leadFeatureTypeText: {
		_text: {
			en: 'Text',
			cn: '文本',
		},
		_mount: {
			en: 'Mounting',
			cn: '本体',
		},
		_ic_lead: {
			en: 'IC Lead',
			cn: 'IC引脚',
		},
		_ic_lead_gap: {
			en: 'IC Lead Gap',
			cn: 'IC引脚间隙',
		},
		_solder: {
			en: 'Solder',
			cn: '焊锡',
		},
		_barcode: {
			en: 'Barcode',
			cn: '条形码',
		},

	},
	featureTypeDisplayText: {
		_text: {
			en: 'Text Verification',
			cn: '文本验证',
		},
		_mount: {
			en: 'Mounting Inspection',
			cn: '贴装检测',
		},
		_ic_lead: {
			en: 'Lead Inspection',
			cn: 'IC引脚检测',
		},
		_solder: {
			en: 'Solder Inspection',
			cn: '焊锡检测',
		},
		_barcode: {
			en: 'Barcode Inspection',
			cn: '条形码检测',
		},
		polarity: {
			en: 'Polarity',
			cn: '极性检测',
		},
	},
	inferenceErrorType: {
		errorNotFound: {
			en: 'Error not found',
			cn: '错误未找到',
		},
		"mounting_inspection_2d/basic": {
			en: 'Significant Defect Check',
			cn: '显著缺陷检测',
		},
		"mounting_inspection_2d/polarity": {
			en: 'Polarity Check',
			cn: '极性检测',
		},
		"mounting_inspection_2d/defect": {
			en: 'Minor Defect Check',
			cn: '细微缺陷缺陷',
		},
		"solder_inspection_2d/basic":{
			en: 'Solder Fillet 2d Check',
			cn: '2D 焊锡检测',
		},
		"lead_inspection_2d/lead": {
			en: 'IC Lead Defect Check',
			cn: '引脚缺陷检测',
		},
        "lead_inspection_2d/bridge": {
			en: 'Connect Bridge Check',
			cn: '引脚连焊检测',
		},
        "lead_inspection_2d_v2/solder": {
			en: 'IC Lead Solder Check',
			cn: '引脚焊锡检测'
		},
        "lead_inspection_2d_v2/lifted_lead": {
			en: 'Lifted Lead Check',
			cn: '引脚翘起检测'
		},
        "text_verification/basic": {
			en: 'Text Check',
			cn: '文字检测'
		},
	    "mounting_inspection_3d/shift":  {
			en: 'Mounting Shift Check',
			cn: '本体偏移检测'
		},
        "mounting_inspection_3d/tilt_angle": {
			en: 'Mounting Tilt Check',
			cn: '本体倾斜检测'
		},
        "mounting_inspection_3d/height": {
			en: 'Mounting Height Check',
			cn: '本体高度检测'
		},
		"solder_inspection/basic": {
			en: '3d Solder Check',
			cn: '3d焊锡检测'
		},
		"lead_inspection_3d/height": {
			en: 'IC Lead Height Check',
			cn: '引脚高度检测'
		},
		missing_component: {
			en: 'Missing Component',
			cn: '缺件',
		},
		turnover: {
			en: 'Turnover',
			cn: '反件',
		},
		lifted_component: {
			en: 'Lifted Component',
			cn: '元件翘起',
		},
		solder_fillet_error: {
			en: 'Solder Fillet Error',
			cn: '焊料倒角错误',
		},
		'tombstone/billboard': {
			en: 'Tombstone/Billboard',
			cn: '立碑/侧立',
		},
		no_error: {
			en: 'No Error Detected',
			cn: '未检测到错误',
		},
		excessive_solder: {
			en: 'Excessive Solder',
			cn: '多锡',
		},
		lead_threshold: {
			en: 'Lead Threshold',
			cn: '引脚阈值',
		},
		bridge_threshold: {
			en: 'Bridge Threshold',
			cn: '桥接阈值',
		},
		shift_x: {
			en: 'Shift X',
			cn: 'X轴偏移',
		},
		shift_y: {
			en: 'Shift Y',
			cn: 'Y轴偏移',
		},
		tilt_angle: {
			en: 'Tilt Angle',
			cn: '倾斜角度',
		},
		threshold: {
			cn: '显著缺陷检测',
			en: 'Significant Defect Check',
		},
		defect_check_threshold: {
			cn: '细微缺陷检测',
			en: 'Subtle Defect Check',
		},
		no_error: {
			en: 'No error',
			cn: '无错误',
		},
		wrong_component: {
			en: 'Wrong component',
			cn: '错件',
		},
		wrong_polarity: {
			en: 'Wrong polarity',
			cn: '极性',
		},
		damaged_component: {
			en: 'Damaged component',
			cn: '损件',
		},
		height: {
			en: 'Height',
			cn: '高度错误',
		},
		misalignment: {
			en: 'Misalignment',
			cn: '偏移',
		},
		lifted_chip: {
			en: 'Lifted chip',
			cn: '芯片翘起',
		},
		solder_bridge: {
			en: 'Solder bridge',
			cn: '焊锡桥联',
		},
		IC_misalignment : {
			en: 'IC misalignment',
			cn: 'IC偏移',
		},
		IC_lead_solder_fillet_error: {
			en: 'IC lead solder fillet error',
			cn: 'IC引脚焊锡倒角错误',
		},
		damaged_lead: {
			en: 'Damaged lead',
			cn: '引脚损坏',
		},
		lifted_lead: {
			en: 'Lifted lead',
			cn: '引脚翘起',
		},
		open_solder: {
			en: 'Open solder',
			cn: '空焊',
		},
		exccess_solder: {
			en: 'Excess solder',
			cn: '多锡',
		},
		insufficient_solder: {
			en: 'Insufficient solder',
			cn: '少锡',
		},
		wrong_text: {
			en: 'Wrong text',
			cn: '文本错误',
		},
		text: {
			en: 'Wrong Text',
			cn: '文本错误',
		},
		unknown: {
			en: 'Unknown error type',
			cn: '未知错误类型',
		},
	},
	globalRetrainReminder: {
		title: {
			en: 'Retrain Reminder',
			cn: '重新训练提醒',
		},
		youHaveUpdatedProductSoMightWantToRetrainModel: {
			en: 'You have updated ${productName} PCB program, so you might want to retrain the model. You can manually retrain the model in teach page.',
			cn: '您已更新 ${productName} PCB程序，因此您可能想要重新训练模型。您可以在教学页面手动重新训练模型。',
		}
	},
	autoProgramming: {
		registerProduct: {
			en: 'Register Product',
			cn: '注册产品',
		},
		manualProgramming: {
			en: 'Manual programming',
			cn: '手动编程',
		},
		runAutoProgram: {
			en: 'Run auto programming',
			cn: '运行自动编程',
		},
		skipToTemplateEditor: {
			en: `Don't use auto programming, skip to template editor`,
			cn: '不使用自动编程，跳至模板编辑器',
		},
		reAnalyze: {
			en: 'Re-analyze',
			cn: '重新分析',
		},
		semiAutoProgramFail: {
			en: 'Failed to auto program, system will try to parse the CAD file and you can align the object coordinates and try auto program again.',
			cn: '自动编程失败, 系统将尝试解析CAD文件，您可以对齐对象坐标并再次尝试自动编程。',
		},
		wahtAreTheTypes: {
			en: 'What are the type(s) of object to focus on?',
			cn: '要关注的封装类型是什么？',
		},
		allModelledInspectionTypes: {
			en: 'AI-modelled inspection types: ',
			cn: '自动编程检测类型：',
		},
		lightSettings: {
			en: 'Light Settings',
			cn: '光源设置',
		},
		defineRegion: {
			en: 'Define Region',
			cn: '定义区域',
		},
		setInspectionRegion: {
			en: 'Set Inspection Region',
			cn: '设置检测区域',
		},
		defineWhereToAuto: {
			en: 'Define where to auto-generate ROIs',
			cn: '定义自动编程的区域',
		},
		performAFullCapture: {
			en: 'Perform a full capture before adjusting the 2D and 3D settings.',
			cn: '在调整2D和3D设置之前执行完整拍摄。',
		},
		performAFullCapture2d: {
			en: 'Perform a full 2D capture before adjusting the 2D settings.',
			cn: '在调整2D设置之前执行完整2D拍摄。',
		},
		full2DAnd3D: {
			en: 'Full 2D and 3D Capture',
			cn: '完整2D和3D拍摄',
		},
		PCBDimensions: {
			en: 'PCB Dimensions',
			cn: 'PCB尺寸',
		},
		capture2D: {
			en: 'Capture 2D Image',
			cn: '拍摄2D图像',
		},
		moveCamera: {
			en: 'Move Camera',
			cn: '移动相机',
		},
		inputCameraPosition: {
			en: 'Input Camera Position',
			cn: '输入相机位置',
		},
		measureWithCamera: {
			en: 'Measure with Camera',
			cn: '使用相机测量',
		},
		moveTheCameraToThree: {
			en: 'Move the camera to three PCB corners to capture and calculate accurate dimensions.',
			cn: '将相机移动到三个PCB角落以捕获并计算准确的尺寸。',
		},
		enterTheDimensionIfKnown: {
			en: 'Enter the dimensions if known.',
			cn: '如果已知，请输入尺寸。',
		},
		conveyorWidth: {
			en: 'Conveyor Width',
			cn: '传送带宽度',
		},
		limitTo: {
			en: '* Limit to 50 - 350 mm',
			cn: '* 限制在50 - 350 mm',
		},
		exitTeach: {
			en: 'Exit Programming',
			cn: '退出向导',
		},
		arrayRow: {
			en: 'Array Row',
			cn: '行',
		},
		arrayCol: {
			en: 'Array Column',
			cn: '列',
		},
		panelWidth: {
			en: 'Panel Width (mm):',
			cn: '板宽度 (mm):',
		},
		panelLength: {
			en: 'Panel Length (mm):',
			cn: '板长度 (mm):',
		},
		boardInfo: {
			en: 'Board Info',
			cn: '拼板信息',
		},
		productName: {
			en: 'Product Name',
			cn: '产品名称',
		},
		basicInfo: {
			en: 'Basic Info',
			cn: '基本信息',
		},
		placePCBAOnConveyor: {
			en: 'Place PCB on conveyor',
			cn: '将PCB放置在传送带上',
		},
		getDimension: {
			en: 'Get Dimension',
			cn: '获取尺寸',
		},
		capture: {
			en: 'Capture',
			cn: '拍摄',
		},
		addCAD: {
			en: 'Add CAD (Optional)',
			cn: '添加CAD (可选)',
		},
		quickTeachProgramming: {
			en: 'Programming with Wizard',
			cn: '自动编程向导',
		},
		autoGenerateRois: {
			en: 'Auto-generate ROIs on PCB based on your selected inspection type and package.',
			cn: '根据您选择的检测类型和封装自动生成PCB上的ROI。',
		},
		// 检测类型翻译
		inspectionTypes: {
			mount: {
				en: 'Mount',
				cn: '本体',
			},
			polarity: {
				en: 'Polarity',
				cn: '极性',
			},
			solder: {
				en: 'Solder',
				cn: '焊点',
			},
			ic_lead: {
				en: 'IC Lead',
				cn: 'IC引脚',
			},
			TEXT: {
				en: 'Text',
				cn: '文字',
			},
		},
		// 组件封装翻译
		componentTypes: {
			Capacitor: {
				en: 'Capacitor',
				cn: '电容器',
			},
			Resistor: {
				en: 'Resistor',
				cn: '电阻器',
			},
			Multilayer_Chip_Inductor: {
				en: 'Multilayer Chip Inductor',
				cn: '多层片式电感器',
			},
			LED: {
				en: 'LED',
				cn: 'LED',
			},
			SOT_SOD: {
				en: 'SOT/SOD',
				cn: 'SOT/SOD',
			},
			SOIC_SOP_TSOP_TSSOP_MSOP: {
				en: 'SOIC/SOP/TSOP/TSSOP/MSOP',
				cn: 'SOIC/SOP/TSOP/TSSOP/MSOP',
			},
			Array_Resistor: {
				en: 'Array Resistor',
				cn: '排阻',
			},
			Aluminum_Capacitor: {
				en: 'Aluminum Capacitor',
				cn: '铝电解电容',
			},
			Tantalum_Capacitor: {
				en: 'Tantalum Capacitor',
				cn: '钽电容',
			},
			Power_Inductor: {
				en: 'Power Inductor',
				cn: '功率电感',
			},
			QFP: {
				en: 'QFP',
				cn: 'QFP',
			},
			QFN: {
				en: 'QFN',
				cn: 'QFN',
			},
			'TO-263_TO-252': {
				en: 'TO-263/TO-252',
				cn: 'TO-263/TO-252',
			},
		},
	},
	notification: {
		error: {
			pleaseDefineTopLeftMarker: {
				en: 'Please define top left marker.',
				cn: '请定义左上角标记。',
			},
			pleaseDefineTopRightMarker: {
				en: 'Please define top right marker.',
				cn: '请定义右上角标记。',
			},
			pleaseDefineBottomLeftMarker: {
				en: 'Please define bottom left marker.',
				cn: '请定义左下角标记。',
			},
			startInferenceWithoutMarker: {
				en: 'Failed to start inference, please define alignment marker first.',
				cn: '启动推理失败，请先定义对齐标记。',
			},
			exportProductAsFile: {
				en: 'Failed to export product as file.',
				cn: '导出产品为文件失败。',
			},
			retrainFailed: {
				en: 'Retrain failed.',
				cn: '重新训练失败。',
			},
			autoGenerateAgentParams: {
				en: 'Failed to auto generate agent params.',
				cn: '自动生成Agent参数失败。',
			},
			createComponentVariation: {
				en: 'Failed to create component variation.',
				cn: '创建衍生createComponentVariation元件失败。',
			},
			selectInspectionRegion: {
				en: 'Please select an inspection region.',
				cn: '请选择一个检测区域。',
			},
			pleaseDefineThreeMarkers: {
				en: 'Please define three markers.',
				cn: '请定义三个标记。',
			},
			invalidMarkerPoint: {
				en: 'Invalid marker point, please select a point within the image.',
				cn: '无效的标记点，请选择图像内的点。',
			},
			registerGoldenArray: {
				en: 'Failed to register golden array.',
				cn: '注册基准阵列失败。',
			},
			runFullAutoProgramming: {
				en: 'Failed to run full auto programming.',
				cn: '运行完整自动编程失败。',
			},
			featureBoxTooSmall: {
				en: 'The feature box is too small, please draw a box with length and width more than ${minFeatureBoxLength} pixels.',
				cn: '子元件框太小，请绘制长度和宽度大于 ${minFeatureBoxLength}} 像素的框。',
			},
			applyGroupLinkAction: {
				en: 'Failed to link group.',
				cn: '连接分组失败。',
			},
			getAgentParamInGroup: {
				en: 'Failed to get agent parameter in group.',
				cn: '获取组中的Agent参数失败。',
			},
			exportMesResult: {
				en: 'Failed to export MES result.',
				cn: '导出MES结果失败。',
			},
			failToCapturePleaseEnsureCameraHasMovedToDesignatedPosition: {
				en: 'Failed to capture, please ensure the camera has moved to the designated position.',
				cn: '拍摄失败，请确保相机已移动到指定位置。',
			},
			selectRefDesignatorColumn: {
				en: 'Please select the reference designator column.',
				cn: '请选择标识列。',
			},
			selectRotationColumn: {
				en: 'Please select the rotation column.',
				cn: '请选择旋转列。',
			},
			selectedPCBAProgramNeedsToBeRetrained: {
				en: 'The selected PCB program needs to be retrained before inference.',
				cn: '所选的PCB程序需要在推理之前重新训练。',
			},
			retrainModel: {
				en: 'Failed to retrain model.',
				cn: '重新训练模型失败。',
			},
			removeAccount: {
				en: 'Failed to remove account. ',
				cn: '删除账户失败。',
			},
			newAccount: {
				en: 'Failed to create a new account.',
				cn: '创建新账户失败。',
			},
			pleaseUseAStrongPwd: {
				en: 'Please use a strong password with at least 8 characters, one including uppercase character, one lowercase character, one number.',
				cn: '请使用至少8个字符的强密码，包括一个大写字母、一个小写字母和一个数字。',
			},
			renewAccessToken: {
				en: 'Failed to renew access token.',
				cn: '更新访问令牌失败。',
			},
			pleaseFillInAllRequiredFields: {
				en: 'Please fill in all required fields.',
				cn: '请填写所有必填字段。',
			},
			loginFailed: {
				en: 'Login failed, please check your username and password.',
				cn: '登录失败，请检查您的用户名和密码。',
			},
			getInspectedComponent: {
				en: 'Failed to get inspected component',
				cn: '获取检测元件失败',
			},
			inspectionSessionId: {
				en: 'Failed to get inspection session id',
				cn: '获取检测任务ID失败',
			},
			conveyorNotIdle: {
				en: 'Conveyor is not idle, please stop the conveyor before teaching.',
				cn: '传送带未空闲，请在编程之前停止传送带。',
			},
			pleaseEnterAValidValue: {
				en: 'Please enter a valid value.',
				cn: '请输入有效值。',
			},
			nameExists: {
				en: 'This name already exists.',
				cn: '此名称已被占用',
			},
			getAllFeatures: {
				en: 'Failed to get all features',
				cn: '获取所有子元件失败',
			},
			conveyorInUsedWhenTeach: {
				en: 'At least one conveyor is in used, please release all used conveyor before teaching.',
				cn: '至少有一个传送带正在使用，请在编程之前释放所有使用中的传送带。',
			},
			annotateGroup: {
				en: 'Failed to annotate group',
				cn: '反馈组失败',
			},
			getFeatureByFeatureId: {
				en: 'Failed to get feature by feature id',
				cn: '获取子元件失败',
			},
			scanBarcode: {
				en: 'Failed to scan barcode',
				cn: '扫描条形码失败',
			},
			deleteSession: {
				en: 'Failed to delete session',
				cn: '删除检测任务失败',
			},
			detectMarkerCenter: {
				en: 'Failed to detect marker center, please refine the marker position',
				cn: '检测标记中心失败，请调整标记位置',
			},
			exportInspectedFeature: {
				en: 'Failed to export inspected feature',
				cn: '导出检测子元件失败',
			},
			conveyorNotRunningInference: {
				en: 'Conveyor is not running inference',
				cn: '传送带未运行推理',
			},
			stopContinuousInference: {
				en: 'Failed to stop continuous inference',
				cn: '停止连续推理失败',
			},
			removeFeatureFromComponent: {
				en: 'Failed to remove feature from component',
				cn: '从元件中移除子元件失败',
			},
			copyFeatureIntoComponent: {
				en: 'Failed to copy feature into component',
				cn: '复制子元件到元件失败',
			},
			copyComponent: {
				en: 'Failed to copy component',
				cn: '复制元件失败',
			},
			trainingExampleNotFound: {
				en: 'Training example not found',
				cn: '训练示例未找到',
			},
			reevaluateExample: {
				en: 'Failed to reevaluate example',
				cn: '重新评估示例失败',
			},
			markTrainingExample: {
				en: 'Failed to mark training example',
				cn: '标记训练示例失败',
			},
			getDataExample: {
				en: 'Failed to get data example',
				cn: '获取数据示例失败',
			},
			sessionNotFound: {
				en: 'Session not found',
				cn: '未找到检测任务',
			},
			featureRoiShouldOverlapMounting2dPolarityRoi: {
				en: 'Feature ROI should overlap with mounting 2D polarity ROI',
				cn: '子元件ROI应与2D极性ROI重叠',
			},
			featureRoiShouldOverlapMounting2dMaskRoi: {
				en: 'Feature ROI should overlap with mounting 2D mask ROI',
				cn: '子元件ROI应与2D掩模ROI重叠',
			},
			deleteInspectionRecord: {
				en: 'Failed to delete inspection record',
				cn: '删除检测记录失败',
			},
			getInferenceStatus: {
				en: 'Failed to get inference status',
				cn: '获取检测状态失败',
			},
			getInspections: {
				en: 'Failed to get inspections',
				cn: '获取检测失败',
			},
			getSession: {
				en: 'Failed to get inspection task',
				cn: '获取检测任务失败',
			},
			refetchInspectedFeatures: {
				en: 'Failed to refetch inspected features',
				cn: '重新获取子元件失败',
			},
			cancelFeedback: {
				en: 'Failed to cancel feedback',
				cn: '取消反馈失败',
			},
			provideFeedback: {
				en: 'Failed to provide feedback',
				cn: '提供反馈失败',
			},
			theExtendedRoiShouldOverlapProfileRoiCompletely: {
				en: 'The extended ROI should completely overlap the profile ROI, and the boundary of the latter should not exceed the range of the former.',
				cn: '扩展ROI应完全覆盖轮廓ROI，后者的边界不得超出前者的范围。',
			},
			physicalCoordMap: {
				en: 'Failed to convert physical coordinates to pixel coordinates',
				cn: '物理至像素坐标转换失败',
			},
			extendedRoiDimensionExceedLimit: {
				en: 'Extended ROI dimension exceed limit',
				cn: '扩展ROI尺寸超出限制',
			},
			thisRoiShouldOverlapTheFeatureRoiCompletely: {
				en: 'This roi box should completely overlap the feature roi box, and the boundary of the latter should not exceed the range of the former.',
				cn: '此roi框应完全覆盖子元件roi框，后者的边界不得超出前者的范围。',
			},
			thisRoiHasToBePleacedWithinTheFeatureRoi: {
				cn: '子元件roi框应完全覆盖此roi框，后者的边界不得超出前者的范围。',
				en: 'The feature roi box should completely contain this roi box, and the boundary of the latter should not exceed the range of the former.',
			},
			provideGoodFeedbackForAllPredictedAsDefectInspectionItem: {
				en: 'Failed to provide good feedback for all inspection item predicted as defective',
				cn: '为所有检测项目预测为缺陷提供良好反馈失败',
			},
			enableContinuousInspection: {
				en: 'Failed to enable continuous inspection',
				cn: '启用连续检测失败',
			},
			getLastestInspection: {
				en: 'Failed to get latest inspection',
				cn: '获取最新检测失败',
			},
			singleInspectionTrigger: {
				en: 'Failed to trigger single inspection',
				cn: '触发单个检测失败',
			},
			getRunningInference: {
				en: 'Failed to get running inference',
				cn: '获取运行中推理任务',
			},
			unableToFindConveyorId: {
				en: 'Unable to find conveyor ID',
				cn: '无法找到传送带ID',
			},
			inferenceStatus: {
				en: 'Failed to get inference status',
				cn: '获取推理状态失败',
			},
			stopSession: {
				en: 'Failed to stop inspection task',
				cn: '停止检测任务失败',
			},
			startInspection: {
				en: 'Failed to start inspection',
				cn: '开始检测失败',
			},
			featureROIBoundaryLengthOverLimit: {
				en: 'Feature ROI boundary length is over the limit',
				cn: '子元件ROI边界长度超出限制',
			},
			deleteComponent: {
				en: 'Failed to delete component',
				cn: '删除元件失败',
			},
			deleteFeature: {
				en: 'Failed to delete feature',
				cn: '删除子元件失败',
			},
			updateFeature: {
				en: 'Failed to update feature',
				cn: '更新子元件失败',
			},
                updateComponent: {
                        en: 'Failed to update component',
                        cn: '更新元件失败',
                },
                saveComponentTemplate: {
                        en: 'Failed to save template to private library',
                        cn: '保存模板到私有库失败',
                },
                newComponent: {
                        en: 'Failed to create new component',
                        cn: '创建新元件失败',
                },
			registerFeature: {
				en: 'Failed to register feature',
				cn: '注册子元件失败',
			},
			enterFirstAndLastRow: {
				en: 'Please enter first and last row index',
				cn: '请输入第一行和最后一行索引',
			},
			selectADelimeter: {
				en: 'Please select a delimiter',
				cn: '请选择一个分隔符',
			},
			selectAUnitMultiplier: {
				en: 'Please select a unit',
				cn: '请选择一个单位',
			},
			selectAllColumns: {
				en: 'Please select package number, center x, center y columns',
				cn: '请选择封装、中心X坐标、中心Y坐标列',
			},
			selectBotLayerIdentifier: {
				en: 'Please enter a bottom layer identifier',
				cn: '请输入底层标识符',
			},
			CADUploadfileExtensionNotSupported: {
				en: 'File extension not supported, please upload a .txt, .csv, .tsv file',
				cn: '文件扩展名不支持，请上传 .txt, .csv, .tsv 文件',
			},
			addComponent: {
				en: 'Failed to add component',
				cn: '添加元件失败',
			},
			addFeature: {
				en: 'Failed to add feature',
				cn: '添加子元件失败',
			},
			selectATemplateFirst: {
				en: 'Please select a template first',
				cn: '请先选择一个模板',
			},
			mapComponentTemplateCoord: {
				en: 'Failed to map component template coordinates',
				cn: '映射元件模板坐标失败',
			},
			getComponentTemplate: {
				en: 'Failed to get component template',
				cn: '获取元件模板失败',
			},
			getComponent: {
				en: 'Failed to get component',
				cn: '获取元件失败',
			},
			parseCAD: {
				en: 'Failed to parse CAD file',
				cn: '解析CAD文件失败',
			},
			parseResultIsEmpty: {
				en: 'Parse result is empty',
				cn: '解析结果为空',
			},
			pleaseSelectADelimiter: {
				en: 'Please select a delimiter',
				cn: '请选择一个分隔符',
			},
			uploadFile: {
				en: 'Failed to upload file',
				cn: '上传文件失败',
			},
			updateMarker: {
				en: 'Failed to update marker',
				cn: '更新标记失败',
			},
			refetchMarkers: {
				en: 'Failed to refetch markers',
				cn: '重新获取标记失败',
			},
			addMarker: {
				en: 'Failed to add marker',
				cn: '添加标记失败',
			},
			productInspectablesRegister: {
				en: 'Failed to register product',
				cn: '注册产品失败',
			},
			pleaseCapture2DImageFirst: {
				en: 'Please capture 2D image first',
				cn: '请先拍摄2D图像',
			},
			addProduct: {
				en: 'Failed to add product',
				cn: '添加产品失败',
			},
			getAllConveyorStatus: {
				en: 'Failed to get all conveyor status',
				cn: '获取所有传送带状态失败',
			},
			pleaseSelectAConveyor: {
				en: 'Please select a conveyor',
				cn: '请选择一组轨道',
			},
			acquireConveyorControl: {
				en: 'Failed to acquire conveyor control',
				cn: '获取传送带控制失败',
			},
			invalidConveyorWidth: {
				en: 'Invalid conveyor width',
				cn: '无效的传送带宽度',
			},
			resizeConveyor: {
				en: 'Failed to set conveyor width',
				cn: '设置传送带宽度失败',
			},
			conveyorAlreadyReleased: {
				cn: '传送带使用权已释放',
				en: 'Conveyor control already released',
			},
			releaseConveyorControl: {
				en: 'Failed to release conveyor control',
				cn: '释放传送带控制失败',
			},
			conveyorAccessTokenEmpty: {
				en: 'Conveyor access token is empty',
				cn: '传送带访问令牌为空',
			},
			conveyorOperation: {
				en: 'Failed to submit conveyor operation',
				cn: '提交传送带操作失败',
			},
			updateProduct: {
				en: 'Failed to update product information',
				cn: '更新产品信息失败',
			},
			acquireCameraControl: {
				en: 'Failed to acquire camera control',
				cn: '获取相机控制失败',
			},
			releaseCameraControl: {
				en: 'Failed to release camera control',
				cn: '释放相机控制失败',
			},
			moveCamera: {
				en: 'Failed to move camera',
				cn: '移动相机失败',
			},
			getCameraCaptureFrameUri: {
				en: 'Failed to get camera capture frame URI',
				cn: '获取相机拍摄帧URI失败',
			},
			selectPCBDimensionPixel: {
				en: 'Please select bottom-left and top-right pixels for PCB dimension',
				cn: '请选择PCB尺寸的左下角和右上角像素',
			},
			invalidPCBDimensionPoint: {
				en: 'Invalid PCB dimension points',
				cn: '无效的PCB尺寸点',
			},
			fetchCroppedThreeDDisplay: {
				en: 'Failed to fetch cropped 3D display',
				cn: '获取裁剪3D显示失败',
			},
			measureProductDimension: {
				en: 'Failed to measure product dimension',
				cn: '测量产品尺寸失败',
			},
			failToGetSystemConfig: {
				en: 'Failed to get system config',
				cn: '获取系统配置失败',
			},
			failToParseThisJsonToObject: {
				en: 'Failed to parse this json to object',
				cn: '无法将此json解析为对象',
			},
			failToUpdateAllSystemConfigFiles: {
				en: 'Fail to update system config files',
				cn: '更新系统配置文件失败',
			},
		},
		success: {
			updateMarker: {
				en: 'Marker updated successfully',
				cn: '标记更新成功',
			},
			createComponentVariation: {
				en: 'Created component variation successfully',
				cn: '创建衍生元件成功',
			},
			changesSaved: {
				en: 'Changes saved successfully',
				cn: '更改已成功保存',
			},
			applyGroupLinkAction: {
				en: 'Linked group successfully.',
				cn: '连接分组成功。',
			},
			exportMesResult: {
				en: 'Exported MES result successfully.',
				cn: 'MES结果导出成功。',
			},
			deleteSession: {
				en: 'Session deleted successfully',
				cn: '检测任务删除成功',
			},
			modelUpdate: {
				en: 'Please update the model',
				cn: '请更新模型',
			},
			detectMarkerCenter: {
				en: 'Marker center detected successfully',
				cn: '标记中心检测成功',
			},
			reevaluateExample: {
				en: 'Reevaluate example successfully',
				cn: '重新评估示例成功',
			},
			continuousInspectionEnabled: {
				en: 'Continuous inspection enabled successfully.',
				cn: '连续检测成功启用。',
			},
			continuousInspectionStopped: {
				en: 'Continuous inspection stopped successfully, and the current inference session is still running you can trigger single inspection.',
				cn: '连续检测成功停止，当前推理任务仍在运行，您可以触发单个检测。',
			},
                        updateComponent: {
                                en: 'Component updated successfully',
                                cn: '元件更新成功',
                        },
                        saveComponentTemplate: {
                                en: 'Template saved to private library',
                                cn: '模板已保存到私有库',
                        },
                        retrainFinished: {
                                en: 'Retrain finished successfully',
                                cn: '重新训练成功',
                        },
			singleInspectionTrigger: {
				en: 'Single inspection processed',
				cn: '已完成单个检测',
			},
			updateProduct: {
				en: 'Product updated successfully',
				cn: '产品更新成功',
			},
			addProduct: {
				en: 'Product added successfully',
				cn: '产品添加成功',
			},
			acquireConveyorControl: {
				en: 'Conveyor control acquired successfully',
				cn: '传送带控制获取成功',
			},
			resizeConveyor: {
				en: 'Conveyor width set successfully',
				cn: '传送带宽度设置成功',
			},
			releaseConveyorControl: {
				en: 'Conveyor control released successfully',
				cn: '传送带控制释放成功',
			},
			autoReleaseConveyorControl: {
				en: 'Conveyor control auto-released',
				cn: '传送带控制已自动释放',
			},
			moveCamera: {
				en: 'Move camera request sent successfully',
				cn: '移动相机请求已发送成功',
			},
			systemConfigFilesUpdated: {
				en: 'System config files have been updated',
				cn: '系统配置文件已更新',
			},
		},
	},
	conveyorInUse: {
		conveyorInUse: {
			en: 'Conveyor in use',
			cn: '传送带正在被占用',
		},
		stopTaskAnd: {
			en: 'Stop task and offload PCB',
			cn: '停止任务并卸下PCB',
		},
		theSelectedConveyor: {
			en: 'The selected conveyor is being used by another task. Please stop the active task to proceed.',
			cn: '所选传送带正在被另一个任务使用。请停止正在运行的任务以继续。',
		},
	},
	existingComponentsFound: {
		thisActionWillReplaceExistingComponents: {
			en: 'This action will replace existing components on the PCB. Do you want to proceed?',
			cn: '此操作将替换PCB上的现有元件，您要继续吗？',
		},
		existingComponentsFound: {
			en: 'Existing Components Found',
			cn: '发现现有元件',
		},
		existingComponentsDetectedOn: {
			en: 'Existing components detected on the PCB. How would you like to proceed?',
			cn: `检测到PCB上的现有元件，请选择如何处理。`,
		},
		replaceAll: {
			en: 'Replace all existing components (Delete all and import new ones)',
			cn: '替换所有现有元件（删除所有并导入新元件）',
		},
		onlyAddNew: {
			en: 'Only add new components (Keep existing ones and add only new ones)',
			cn: '仅添加新元件（保留现有元件并仅添加新元件）',
		},
		replaceAllExisting: {
			en: 'Replace all existing components',
			cn: '替换所有现有元件',
		},
		onlyAddNewComponents: {
			en: 'Only add new components',
			cn: '仅添加新元件',
		},
	},
	addFromLibrary: {
		findTemplate: {
			en: 'Find Template',
			cn: '查找模板',
		},
		componentLibrary: {
			en: 'Component Library',
			cn: '元件库',
		},
		public: {
			en: 'Public',
			cn: '公共',
		},
		private: {
			en: 'Private',
			cn: '私有',
		},
		package: {
			en: 'Package',
			cn: '封装',
		},
		category: {
			en: 'Category',
			cn: '类别',
		},
		templatesFound: {
			en: 'Templates Found',
			cn: '找到模板',
		},
		useThisTemplate: {
			en: 'Use this Template',
			cn: '使用此模板',
		},
		componentInfo: {
			en: 'Component Info',
			cn: '元件信息',
		},
		partNumber: {
			en: 'Part Number',
			cn: '料号',
		},
		description: {
			en: 'Description',
			cn: '描述',
		},
		componentLength: {
			en: 'Component Length',
			cn: '元件长度',
		},
		componentWidth: {
			en: 'Component Width',
			cn: '元件宽度',
		},
	},
	newInspectionTask: {
		runTaskByBtnTrigger: {
			en: 'Run task by button trigger',
			cn: '通过按钮触发运行任务',
		},
		runTaskContinuously: {
			en: 'Auto run task continuously',
			cn: '自动连续运行任务',
		},
		selectTaskMode: {
			en: 'Select Task Mode',
			cn: '选择任务模式',
		},
		newInspectionTask: {
			en: 'New Inspection Task',
			cn: '新检测任务',
		},
		pickAPCB: {
			en: 'Pick a PCB',
			cn: '选择一个PCB',
		},
		conveyor: {
			en: 'Conveyor',
			cn: '传送带',
		},
		stopInspectionAuto: {
			en: 'Stop inspection automatically for manual review when a defect is detected ',
			cn: '当检测到缺陷时自动停止检测以进行手动检查',
		},
		refreshStatus: {
			en: 'Refresh Status',
			cn: '刷新状态',
		},
		startInspection: {
			en: 'Start Inspection',
			cn: '开始检测',
		},
		adjustWidth: {
			en: 'Adjust Width',
			cn: '调整宽度',
		},
	},
	updateBackendHost: {
		title: {
			en: 'Update Backend Host Address',
			cn: '更新后端主机地址',
		},
		titleDesc1: {
			cn: '主机地址是指用于连接到后端服务器的URL。此信息储存与系统中，并在每次启动时使用。',
			en: 'The host address is the URL used to connect to the backend server. This information is stored in the system and used at each startup.',
		},
		titleDesc2: {
			cn: '如果遇到当前主机未响应的情况，请更新主机地址。',
			en: 'If you encounter a situation where the current host does not respond, please update the host address.',
		},
		currentHostDidNotResponsePleaseUpdate: {
			en: 'Current host did not respond, please update the host address.',
			cn: '当前主机未响应，请更新主机地址。',
		},

		currentHost: {
			en: 'Current Host:',
			cn: '当前主机:',
		},
		saveAndRefresh: {
			en: 'Save and Refresh',
			cn: '保存并刷新',
		},
	},
	editSystemConfig: {
		exportMESPath: {
			en: 'Export MES path',
			cn: '导出MES路径',
		},
		editSystemConfig: {
			en: 'Edit System Config',
			cn: '编辑系统配置',
		},
		editSystemConfigDesc: {
			en: 'Edit system config to set up the system.',
			cn: '编辑系统配置以设置系统。',
		},
		systemJsonConfig: {
			en: 'System JSON Config',
			cn: '系统JSON配置',
		},
		captureAgent: {
			en: 'Capture Agent',
			cn: '捕获模块',
		},
		captureAgentConfig: {
			en: 'Capture Agent JSON Config',
			cn: '捕获模块JSON配置',
		},
		sensorConfig: {
			en: 'Sensor JSON Config',
			cn: '传感器JSON配置',
		},
		componentDetector: {
			en: 'Component Detector',
			cn: '组件检测器',
		},
		componentDetectorConfig: {
			en: 'Component Detector JSON Config',
			cn: '组件检测器JSON配置',
		},
		inferenceAgent: {
			en: 'Inference Agent',
			cn: '推断模块',
		},
		defectConfig: {
			en: 'Defect JSON Config',
			cn: '缺陷JSON配置',
		},
		depthDiffConfig: {
			en: 'Depth Difference JSON Config',
			cn: '高度差异JSON配置',
		},
		inspection: {
			en: 'Inspection',
			cn: '检测',
		},
		inspectionConfig: {
			en: 'Inspection JSON Config',
			cn: '检测JSON配置',
		},
		system: {
			en: 'System',
			cn: '系统',
		},
		restartReminder: {
			en: 'System restart reminder',
			cn: '系统重启提醒',
		},
		restartReminderDesc: {
			en: 'Please restart the system after editing the system configuration to make the changes effective.',
			cn: '编辑系统配置后，请重新启动系统以使更改生效。',
		},
		dicoveredCameras: {
			en: 'Discovered Cameras',
			cn: '发现的相机',
		},
	},
};
