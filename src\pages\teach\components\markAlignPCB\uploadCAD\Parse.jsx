import { Button, Input, InputNumber, Select, Switch, Table, Upload } from 'antd';
import React, { Fragment, useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { csvParser, readFileWithEncoding } from '../../../../../common/util';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../../../../common/alert';
import { componentAttribute, localStorageKeys, serverHost } from '../../../../../common/const';
import CommonTable from '../../../../../components/CommonTable';
import { useCadParseMutation, useRunSemiAutoProgramMutation } from '../../../../../services/product';
import ConfigAutoProgram from '../../../../../modal/ConfigAutoProgram';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setIsTrainingRunning, setCurTrainingTaskStartTime, setShouldRunReevaluateAfterRetrain, setIsAgentParamRegenRunning } from '../../../../../reducer/setting';
import { useModelUpdateTriggerMutation } from '../../../../../services/inference';
import { modelTypes } from '../../../../../common/const';


const Parse = (props) => {
  const {
    fileObj,
    currentFileUri,
    parseRules,
    setParseRules,
    setCurrentFileUri,
    setFileObj,
    setParsedComponentInfo,
    setCurrentStep,
    productId,
    isInAutoProgramming,
    selectedInspectionTypes,
    autoProgramInspectionRegion,
  } = props;

  const navigate = useNavigate();

  const dispatch = useDispatch();

  const [retrainTrigger] = useModelUpdateTriggerMutation();

  const { t } = useTranslation();

  const displayContainerRef = useRef(null);
  const parseRulesRef = useRef(parseRules);

  const handleAutoGenerateAgentParams = async (productId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoGeneratingAgentParams')));

    const res = await retrainTrigger({
      model_types: [
        modelTypes.mounting3DModel,
        modelTypes.lead3DModel,
        modelTypes.solder3DModel,
        modelTypes.lead2DV2Model,
        modelTypes.solder2DModel,
      ],
      golden_product_id: Number(productId),
    });

    if (res.error) {
      aoiAlert(t('notification.error.autoGenerateAgentParams'), ALERT_TYPES.COMMON_ERROR);
      console.error('retrainTrigger error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return false;
    }

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.refetchingFeatures')));
    dispatch(setIsAgentParamRegenRunning(true));
    dispatch(setIsTrainingRunning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
    return true;
  };

  const [cols, setCols] = useState([]);
  const [fileStrContent, setFileStrContent] = useState('');
  const [selectedEncoding, setSelectedEncoding] = useState('auto'); // 新增编码状态
  const [encodingError, setEncodingError] = useState(''); // 编码错误提示
  const [parsedData, setParsedData] = useState([]);
  const [displayDimension, setDisplayDimension] = useState({
    width: 0,
    height: 0,
  });
  const [isConfigAutoProgramOpened, setIsConfigAutoProgramOpened] = useState(false);
  const [cadInfo, setCadInfo] = useState({ rules: {} });

  const [parseCAD] = useCadParseMutation();
  const [runSemiAutoProgram] = useRunSemiAutoProgramMutation();

  const colKeys = [
    'partNumberCol',
    'packageCol',
    'xCol',
    'yCol',
    'botLayerCol',
    'rotationCol',
    'designatorCol',
  ];

  const mapColKeyToAttr = {
    partNumberCol: componentAttribute.partNo,
    packageCol: componentAttribute.packageNo,
    xCol: componentAttribute.x,
    yCol: componentAttribute.y,
    botLayerCol: componentAttribute.layerCol,
    rotationCol: componentAttribute.rotationCol,
    designatorCol: componentAttribute.designator,
  };

  const headerSelectOptions = [
    {
      value: componentAttribute.packageNo,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.packageNo')}
      </span>,
    },
    {
      value: componentAttribute.partNo,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.partNo')}
      </span>,
    },
    {
      value: componentAttribute.x,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.centerX')}
      </span>,
    },
    {
      value: componentAttribute.y,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.centerY')}
      </span>,
    },
    {
      value: componentAttribute.layerCol,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.layerCol')}
      </span>,
    },
    {
      value: componentAttribute.rotationCol,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.rotationDegCol')}
      </span>,
    },
    {
      value: componentAttribute.designator,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.refDesignator')}
      </span>,
    },
    {
      value: null,
      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
        {t('productDefine.ignoreThisCol')}
      </span>,
    }
  ];

  const handleReupload = (file) => {
    if (_.isEmpty(file)) return;

    const upload = async (fileObj) => {
      setFileObj(fileObj);
      const formData = new FormData();
      formData.append('file', fileObj);

      let res;

      try {
        res = await fetch(`${serverHost}/file?extension=${fileObj.name.split('.').pop()}`, {
          method: 'PUT',
          body: formData,
          headers: {
            'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
          }
        });
      } catch (error) {
        console.error(error);
        aoiAlert(t('notification.error.uploadFile'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      const data = await res.json();
      setCurrentFileUri(_.get(data, 'data_uri'));
      setCurrentStep(0);
    };

    upload(file);

    setParsedData([]);

    // prevent antd's upload handle
    return false;
  };

  const handleColTypeChange = (colIdx, type) => {
    if (type === null) {
      // find the key that is assigned to this column
      _.forEach(_.filter(_.keys(parseRulesRef.current), (k) => _.includes(colKeys, k)), (k) => {
        if (_.get(parseRulesRef.current, k) === colIdx) {
          parseRulesRef.current[k] = undefined;
          setParseRules({
            ...parseRulesRef.current,
            [k]: undefined,
          });
        }
      });
      return;
    }

    // search for type that is assigned to this column and set it to undefined
    _.forEach(_.filter(_.keys(parseRulesRef.current), (k) => _.includes(colKeys, k)), (k) => {
      if (_.get(parseRulesRef.current, k) === colIdx) {
        parseRulesRef.current[k] = undefined;
        setParseRules({
          ...parseRulesRef.current,
          [k]: undefined,
        });
      }
    });

    switch (type) {
      case componentAttribute.packageNo:
        setParseRules({
          ...parseRulesRef.current,
          packageCol: colIdx,
        });
        break;
      case componentAttribute.partNo:
        setParseRules({
          ...parseRulesRef.current,
          partNumberCol: colIdx,
        });
        break;
      case componentAttribute.x:
        setParseRules({
          ...parseRulesRef.current,
          xCol: colIdx,
        });
        break;
      case componentAttribute.y:
        setParseRules({
          ...parseRulesRef.current,
          yCol: colIdx,
        });
        break;
      case componentAttribute.layerCol:
        setParseRules({
          ...parseRulesRef.current,
          botLayerCol: colIdx,
        });
        break;
      case componentAttribute.rotationCol:
        setParseRules({
          ...parseRulesRef.current,
          rotationCol: colIdx,
        });
        break;
      case componentAttribute.designator:
        setParseRules({
          ...parseRulesRef.current,
          designatorCol: colIdx,
        });
        break;
      default:
        setParseRules({
          ...parseRulesRef.current,
        });
        break;
    };
  };

  const handleParseFileStr = (
    fileStr,
    delimiter,
    firstRowIdx,
    lastRowIdx,
    botLayerId,
    isIgnoreBotLayer,
    botLayerCol,
  ) => {
    if (_.isEmpty(fileStr)) return;

    const {records, originalRecordCount} = csvParser(
      fileStr,
      delimiter,
      botLayerId,
      isIgnoreBotLayer,
      botLayerCol,
      firstRowIdx,
      lastRowIdx,
    );

    if (_.isEmpty(records)) {
      setParsedData(records);
      aoiAlert(t('notification.error.parseResultIsEmpty'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // also generate columns based on the first row's length
    const firstRow = records[0];
    setCols(_.map(firstRow, (col, idx) => ({
      title: <Select
        popupMatchSelectWidth={false}
        style={{ width: '100%' }}
        size='small'
        options={headerSelectOptions}
        placeholder={<span className='font-source text-[12px] font-normal leading-[150%]'>
          {t('productDefine.selectFieldTypeForThisColumn')}
        </span>}
        onChange={(v) => {
          handleColTypeChange(idx, v);
        }}
        value={_.get(mapColKeyToAttr, _.find(_.filter(_.keys(parseRules), (k) => _.includes(colKeys, k)), (k) => _.get(parseRules, k) === idx), undefined)}
      />,
      render: (text, record, index) => {
        return <span className='font-source text-[12px] font-normal leading-[150%]'>
          {_.get(record, idx, '')}
        </span>
      },
      ellipsis: true,
    })));
    setParsedData(records);
  };

  const rulesSubmit = async (parseRules, currentFileUri, productId) => {
    // console.log('parseRules when submit', parseRules);
    if (_.isEmpty(parseRules) || _.isEmpty(currentFileUri)) return;
    if (_.isEmpty(parseRules.delimiter)) {
      aoiAlert(t('notification.error.selectADelimeter'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isNumber(parseRules.unitMutiplier)) {
      aoiAlert(t('notification.error.selectAUnitMultiplier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (
      // !_.isInteger(parseRules.partNumberCol) ||
      // !_.isInteger(parseRules.packageCol) ||
      !_.isInteger(parseRules.xCol) || !_.isInteger(parseRules.yCol)) {
      aoiAlert(t('notification.error.selectAllColumns'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (_.get(parseRules, 'isIgnoreBotLayer', false) && !_.isString(parseRules, 'botLayerId', null) && !_.isInteger(parseRules.botLayerCol)) {
      aoiAlert(t('notification.error.selectBotLayerIdentifier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.firstRowIndex) || !_.isInteger(parseRules.lastRowIndex)) {
      aoiAlert(t('notification.error.enterFirstAndLastRow'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.designatorCol)) {
      aoiAlert(t('notification.error.selectRefDesignatorColumn'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (!_.isInteger(parseRules.rotationCol)) {
      aoiAlert(t('notification.error.selectRotationColumn'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // init parse we use 0 for tx, ty, rotation
    const rules = {
      delimiter: _.get(parseRules, 'delimiter', ','),
      data_row_begin: _.get(parseRules, 'firstRowIndex'),
      data_row_end: _.get(parseRules, 'lastRowIndex'),
      unit_multiplier: _.get(parseRules, 'unitMutiplier'),
      part_number_col: _.get(parseRules, 'partNumberCol'),
      package_number_col: _.get(parseRules, 'packageCol'),
      x_col: _.get(parseRules, 'xCol'),
      y_col: _.get(parseRules, 'yCol'),
      designator_col: _.get(parseRules, 'designatorCol'),
    };

    // TODO: waiting for backend to support this
    if (_.isUndefined(parseRules.partNumberCol)) {
      delete rules['part_number_col'];
    }

    if (_.isInteger(parseRules.rotationCol)) {
      rules['enable_rotation'] = true;
      rules['rotation_col'] = parseRules.rotationCol;
    }

    if (_.isBoolean(parseRules.isIgnoreBotLayer)) {
      rules['ignore_bot_layer'] = parseRules.isIgnoreBotLayer;
      rules['layer_col'] = parseRules.botLayerCol;
      rules['bot_layer_identifier'] = parseRules.botLayerId;
    }

    const res = await parseCAD({
      file_path: currentFileUri,
      rules,
      product_id: Number(productId),
      tx: 0,
      ty: 0,
      rotation: 0,
    });

    if (_.get(res, 'error', null)) {
      aoiAlert(t('notification.error.parseCAD'), ALERT_TYPES.COMMON_ERROR);
      console.error('parseCAD error:', _.get(res, 'error.message', ''));
      return;
    }

    setParsedComponentInfo(_.get(res, 'data', []));
    setCurrentStep(1);
  };

  const handleRunSemiAutoProgram = async (
    parseRules,
    currentFileUri,
    productId,
    selectedInspectionTypes,
    autoProgramInspectionRegion,
  ) => {
    // console.log('parseRules when submit', parseRules);
    if (_.isEmpty(parseRules) || _.isEmpty(currentFileUri)) return;
    if (_.isEmpty(parseRules.delimiter)) {
      aoiAlert(t('notification.error.selectADelimeter'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isNumber(parseRules.unitMutiplier)) {
      aoiAlert(t('notification.error.selectAUnitMultiplier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (
      // !_.isInteger(parseRules.partNumberCol) ||
      // !_.isInteger(parseRules.packageCol) ||
      !_.isInteger(parseRules.xCol) || !_.isInteger(parseRules.yCol)) {
      aoiAlert(t('notification.error.selectAllColumns'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (_.get(parseRules, 'isIgnoreBotLayer', false) && !_.isString(parseRules, 'botLayerId', null) && !_.isInteger(parseRules.botLayerCol)) {
      aoiAlert(t('notification.error.selectBotLayerIdentifier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.firstRowIndex) || !_.isInteger(parseRules.lastRowIndex)) {
      aoiAlert(t('notification.error.enterFirstAndLastRow'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.designatorCol)) {
      aoiAlert(t('notification.error.selectRefDesignatorColumn'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (!_.isInteger(parseRules.rotationCol)) {
      aoiAlert(t('notification.error.selectRotationColumn'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // init parse we use 0 for tx, ty, rotation
    const rules = {
      delimiter: _.get(parseRules, 'delimiter', ','),
      data_row_begin: _.get(parseRules, 'firstRowIndex'),
      data_row_end: _.get(parseRules, 'lastRowIndex'),
      unit_multiplier: _.get(parseRules, 'unitMutiplier'),
      part_number_col: _.get(parseRules, 'partNumberCol'),
      package_number_col: _.get(parseRules, 'packageCol'),
      x_col: _.get(parseRules, 'xCol'),
      y_col: _.get(parseRules, 'yCol'),
      designator_col: _.get(parseRules, 'designatorCol'),
    };

    // TODO: waiting for backend to support this
    if (_.isUndefined(parseRules.partNumberCol)) {
      delete rules['part_number_col'];
    }

    if (_.isInteger(parseRules.rotationCol)) {
      rules['enable_rotation'] = true;
      rules['rotation_col'] = parseRules.rotationCol;
    }

    if (_.isBoolean(parseRules.isIgnoreBotLayer)) {
      rules['ignore_bot_layer'] = parseRules.isIgnoreBotLayer;
      rules['layer_col'] = parseRules.botLayerCol;
      rules['bot_layer_identifier'] = parseRules.botLayerId;
    }

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.autoProgramming')));

    const res = await runSemiAutoProgram({
      product_id: Number(productId),
      config: {
        component_for_line_item: selectedInspectionTypes,
      },
      step: 0,
      cad_info: {
        file_path: currentFileUri,
        rules,
        product_id: Number(productId),
        tx: 0,
        ty: 0,
        rotation: 0,
      },
      roi: {
        type: 'obb',
        points: [
          {
            x: _.round(_.get(autoProgramInspectionRegion, 'pmin.x', 0), 0),
            y: _.round(_.get(autoProgramInspectionRegion, 'pmin.y', 0), 0),
          },
          {
            x: _.round(_.get(autoProgramInspectionRegion, 'pmax.x', 0), 0) - 1,
            y: _.round(_.get(autoProgramInspectionRegion, 'pmax.y', 0), 0) - 1,
          }
        ],
        center: null,
        angle: 0,
      }
    });

    if (res.error) {
      aoiAlert(t('autoProgramming.semiAutoProgramFail'), ALERT_TYPES.COMMON_ERROR);
      console.error('runSemiAutoProgram error:', _.get(res, 'error.message', ''));

      // run parse CAD to get the coordinates
      const res1 = await parseCAD({
        file_path: currentFileUri,
        rules,
        product_id: Number(productId),
        tx: 0,
        ty: 0,
        rotation: 0,
      });

      if (res1.error) {
        aoiAlert(t('notification.error.parseCAD'), ALERT_TYPES.COMMON_ERROR);
        console.error('parseCAD error:', _.get(res1, 'error.message', ''));
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }

      setParsedComponentInfo(_.get(res1, 'data', []));
      setCurrentStep(1);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    dispatch(setShouldRunReevaluateAfterRetrain({ productId: Number(productId), shouldRun: true}));

    await handleAutoGenerateAgentParams(productId);
    navigate(`/teach?product-id=${productId}&from-auto-programming=true`);
    return;
  };


  useEffect(() => {
    if (_.isEmpty(fileObj)) return;

    const convertToStr = async (fileObj) => {
      try {
        setEncodingError('');
        let content;
        if (selectedEncoding === 'auto') {
          content = await readFileWithEncoding(fileObj);
        } else {
          content = await readFileAsText(fileObj, selectedEncoding);
        }
        setFileStrContent(content);

        if (content.includes('�') || content.includes('锟斤拷')) {
          setEncodingError(t('productDefine.encodingMaybeIncorrect', '编码可能不正确，如果显示乱码请尝试其他编码'));
        }
      } catch (error) {
        console.error('Failed to read file with specified encoding:', error);
        setEncodingError(t('productDefine.encodingError', '读取文件编码失败，请尝试其他编码'));

        try {
          const reader = new FileReader();
          reader.readAsText(fileObj, 'UTF-8');
          const content = await new Promise((resolve) => {
            reader.onload = () => {
              resolve(reader.result);
            };
          });
          setFileStrContent(content);
        } catch (fallbackError) {
          console.error('Fallback to UTF-8 also failed:', fallbackError);
          setFileStrContent('');
        }
      }
    };

    convertToStr(fileObj);
  }, [fileObj, selectedEncoding]);

  const readFileAsText = async (file, encoding = 'UTF-8') => {
    if (encoding === 'UTF-8' || encoding === 'UTF-16LE' || encoding === 'UTF-16BE') {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.readAsText(file, encoding);
      });
    }

    const buffer = await new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.readAsArrayBuffer(file);
    });

    try {
      const decoder = new TextDecoder(encoding);
      return decoder.decode(buffer);
    } catch (error) {
      console.warn(`Failed to decode with ${encoding}, fallback to UTF-8:`, error);
      const decoder = new TextDecoder('UTF-8');
      return decoder.decode(buffer);
    }
  };

  useEffect(() => {
    // console.log('parseRules', parseRules);
    parseRulesRef.current = parseRules;
    if (!_.isEmpty(parsedData)) {
      // also generate columns based on the first row's length
      const firstRow = parsedData[0];
      setCols(_.map(firstRow, (col, idx) => ({
        title: <Select
          popupMatchSelectWidth={false}
          style={{ width: '100%' }}
          size='small'
          options={headerSelectOptions}
          placeholder={<span className='font-source text-[12px] font-normal leading-[150%]'>
            {t('productDefine.selectFieldTypeForThisColumn')}
          </span>}
          onChange={(v) => {
            handleColTypeChange(idx, v);
          }}
          value={_.get(mapColKeyToAttr, _.find(_.filter(_.keys(parseRules), (k) => _.includes(colKeys, k)), (k) => _.get(parseRules, k) === idx), undefined)}
        />,
        render: (text, record, index) => {
          return <span className='font-source text-[12px] font-normal leading-[150%]'>
            {_.get(record, idx, '')}
          </span>
        },
        ellipsis: true,
      })));
    }
  }, [parseRules]);

  useEffect(() => {
    if (!isInAutoProgramming) return;

    // console.log('parseRules when submit', parseRules);
    if (_.isEmpty(parseRules) || _.isEmpty(currentFileUri)) return;
    if (_.isEmpty(parseRules.delimiter)) {
      // aoiAlert(t('notification.error.selectADelimeter'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isNumber(parseRules.unitMutiplier)) {
      // aoiAlert(t('notification.error.selectAUnitMultiplier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (
      // !_.isInteger(parseRules.partNumberCol) ||
      // !_.isInteger(parseRules.packageCol) ||
      !_.isInteger(parseRules.xCol) || !_.isInteger(parseRules.yCol)) {
      // aoiAlert(t('notification.error.selectAllColumns'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (_.get(parseRules, 'isIgnoreBotLayer', false) && !_.isString(parseRules, 'botLayerId', null) && !_.isInteger(parseRules.botLayerCol)) {
      // aoiAlert(t('notification.error.selectBotLayerIdentifier'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.firstRowIndex) || !_.isInteger(parseRules.lastRowIndex)) {
      // aoiAlert(t('notification.error.enterFirstAndLastRow'), ALERT_TYPES.COMMON_ERROR);
      return;
    }
    if (!_.isInteger(parseRules.designatorCol)) {
      // aoiAlert(t('notification.error.selectRefDesignatorColumn'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // init parse we use 0 for tx, ty, rotation
    const rules = {
      delimiter: _.get(parseRules, 'delimiter', ','),
      data_row_begin: _.get(parseRules, 'firstRowIndex'),
      data_row_end: _.get(parseRules, 'lastRowIndex'),
      unit_multiplier: _.get(parseRules, 'unitMutiplier'),
      part_number_col: _.get(parseRules, 'partNumberCol'),
      package_number_col: _.get(parseRules, 'packageCol'),
      x_col: _.get(parseRules, 'xCol'),
      y_col: _.get(parseRules, 'yCol'),
      designator_col: _.get(parseRules, 'designatorCol'),
    };

    // TODO: waiting for backend to support this
    if (_.isUndefined(parseRules.partNumberCol)) {
      delete rules['part_number_col'];
    }

    if (_.isInteger(parseRules.rotationCol)) {
      rules['enable_rotation'] = true;
      rules['rotation_col'] = parseRules.rotationCol;
    }

    if (_.isBoolean(parseRules.isIgnoreBotLayer)) {
      rules['ignore_bot_layer'] = parseRules.isIgnoreBotLayer;
      rules['layer_col'] = parseRules.botLayerCol;
      rules['bot_layer_identifier'] = parseRules.botLayerId;
    }

    setCadInfo({
      file_path: currentFileUri,
      rules,
      product_id: Number(productId),
      tx: 0,
      ty: 0,
      rotation: 0,
    });
  }, [
    parseRules,
    currentFileUri,
    isInAutoProgramming,
    productId,
  ]);

  useEffect(() => {
    if (!displayContainerRef.current) return;

    setDisplayDimension({
      width: displayContainerRef.current.offsetWidth,
      height: displayContainerRef.current.offsetHeight,
    });
  }, []);

  return (
    <Fragment>
    {/* {isInAutoProgramming && (
    <ConfigAutoProgram
      isOpened={isConfigAutoProgramOpened}
      setIsOpened={setIsConfigAutoProgramOpened}
      productId={productId}
      step={0}
      onFinish={async (parsedData) => {
        // this only be called when semi auto programming failed
        setParsedComponentInfo(parsedData);
        setCurrentStep(1);
      }}
      isFullAutoProgram={false}
      cadInfo={cadInfo}
    />
    )} */}
    <div className={`flex ${!isInAutoProgramming ? 'px-[64px]' : ''} gap-8 justify-center flex-1 self-stretch`}>
      <div
        ref={displayContainerRef}
        className='flex self-stretch overflow-auto'
        style={{ width: `calc(100vw - ${!isInAutoProgramming ? '528px' : '950px'})` }}
      >
        {_.isEmpty(parsedData) ?
          <div
            style={{
              height: `${displayDimension.height}px`,
              width: `${displayDimension.width}px`,
              overflow: 'auto',
            }}
          >
            {fileStrContent}
          </div>
        :
          <CommonTable
            cols={cols}
            data={parsedData}
            total={parsedData.length}
          />
        }
      </div>
      <div className='flex w-[348px] flex-col gap-6 self-stretch'>
        <div className='flex w-[346px] p-4 flex-col gap-4 rounded-[4px] bg-[#ffffff0d]'>
          <span className='font-source text-[14px] font-semibold leading-[150%]'>
            {t('productDefine.parseFile')}
          </span>
          <div className='flex flex-col gap-2 self-stretch'>
            <div className='flex flex-col gap-2 self-stretch'>
            </div>
            <Upload
              beforeUpload={handleReupload}
              showUploadList={false}
            >
              <Button>
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('productDefine.reupload')}
                </span>
              </Button>
            </Upload>
            <div className='flex h-[26px] justify-between items-center self-stretch'>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('productDefine.fileEncoding', '文件编码')}
              </span>
              <Select
                size='small'
                style={{ width: '120px' }}
                value={selectedEncoding}
                onChange={(v) => {
                  setSelectedEncoding(v);
                }}
                options={[
                  {
                    value: 'auto',
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.autoDetect', '自动检测')}
                    </span>,
                  },
                  {
                    value: 'UTF-8',
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      UTF-8
                    </span>,
                  },
                  {
                    value: 'GB18030',
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      GB18030
                    </span>,
                  },
                  {
                    value: 'GBK',
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      GBK
                    </span>,
                  },
                  {
                    value: 'UTF-16LE',
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      UTF-16LE
                    </span>,
                  },
                  {
                    value: 'UTF-16BE',
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      UTF-16BE
                    </span>,
                  }
                ]}
              />
            </div>
            {encodingError && (
              <div className='flex items-center gap-1 self-stretch'>
                <span className='font-source text-[11px] font-normal leading-[150%] text-yellow-400'>
                  ⚠️ {encodingError}
                </span>
              </div>
            )}
          </div>
          <div className='flex flex-col gap-4 self-stretch'>
            <div className='flex py-1 flex-col justify-center gap-2 self-stretch'>
              {/* <div className='flex h-[26px] justify-between items-center self-stretch'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('productDefine.delimiter')}
                </span>
                <Select
                  size='small'
                  style={{ width: '120px' }}
                  onChange={(v) => {
                    setParseRules({
                      ...parseRules,
                      delimiter: v,
                    });
                  }}
                  options={[
                    {
                      value: ',',
                      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                        ,
                      </span>,
                    },
                    {
                      value: ';',
                      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                        ;
                      </span>,
                    },
                    {
                      value: '\t',
                      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                        Tab(\t)
                      </span>,
                    },
                    {
                      value: ' ',
                      label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.space')}
                      </span>,
                    }
                  ]}
                />
              </div> */}
              <div className='flex h-[26px] justify-between items-center self-stretch'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('productDefine.firstRowIndex')}
                </span>
                <InputNumber
                  min={1}
                  step={1}
                  controls={false}
                  size='small'
                  onChange={(v) => {
                    setParseRules({
                      ...parseRules,
                      firstRowIndex: v,
                    });
                  }}
                  value={_.get(parseRules, 'firstRowIndex', 1)}
                />
              </div>
              <div className='flex h-[26px] justify-between items-center self-stretch'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('productDefine.lastRowIndex')}
                </span>
                <InputNumber
                  min={1}
                  step={1}
                  controls={false}
                  size='small'
                  onChange={(v) => {
                    setParseRules({
                      ...parseRules,
                      lastRowIndex: v,
                    });
                  }}
                  value={_.get(parseRules, 'lastRowIndex', 1)}
                />
              </div>
              <div className='flex h-[26px] justify-between items-center self-stretch'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('productDefine.ignoreBotLayer')}
                </span>
                <Switch
                  size='small'
                  checked={_.get(parseRules, 'isIgnoreBotLayer', false)}
                  onChange={(v) => {
                    setParseRules({
                      ...parseRules,
                      isIgnoreBotLayer: v,
                    });
                  }}
                />
              </div>
              {_.get(parseRules, 'isIgnoreBotLayer', false) &&
                <Fragment>
                  <div className='flex h-[26px] justify-between items-center self-stretch gap-2'>
                    <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
                      {t('productDefine.ignoreBotLayerIdentifier')}
                    </span>
                    <Input
                      size='small'
                      value={_.get(parseRules, 'botLayerId', 0)}
                      onChange={(e) => {
                        setParseRules({
                          ...parseRules,
                          botLayerId: e.target.value,
                        });
                      }}
                    />
                  </div>
                </Fragment>
              }
            </div>
            <Button
              type='primary'
              onClick={() => {
                if (_.isEmpty(fileStrContent)) return;
                if (_.isEmpty(_.get(parseRules, 'delimiter', ''))) {
                  aoiAlert(t('nofitication.error.pleaseSelectADelimiter'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }
                handleParseFileStr(
                  fileStrContent,
                  _.get(parseRules, 'delimiter', ''),
                  _.get(parseRules, 'firstRowIndex', null),
                  _.get(parseRules, 'lastRowIndex', null),
                  _.get(parseRules, 'botLayerId', 0),
                  _.get(parseRules, 'isIgnoreBotLayer', false),
                  _.get(parseRules, 'botLayerCol', 0),
                );
              }}
            >
              <span className='font-source text-[12px] font-semibold leading-[150%] text-[#333]'>
                {t('productDefine.previewTable')}
              </span>
            </Button>
          </div>
        </div>
        <div className='flex w-[346px] p-4 flex-col gap-4 rounded-[4px] bg-[#ffffff0d]'>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[16px] font-normal leading-[150%]'>
              {t('productDefine.mapData')}
            </span>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.assignColumn')}
            </span>
          </div>
          <div className='flex gap-4 flex-col self-stretch'>
            <div className='flex py-1 justify-between items-center self-stretch'>
              <span className='font-source text-[14px] font-normal leading-[150%]'>
                {t('productDefine.coordinateUnit')}
              </span>
              <Select
                popupMatchSelectWidth={false}
                size='small'
                style={{ width: '120px' }}
                options={[
                  {
                    value: 1,
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.coordUnitMM')}
                    </span>
                  },
                  {
                    value: 39.37007874,
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.coordUnitMIL')}
                    </span>,
                  }
                ]}
                value={_.get(parseRules, 'unitMutiplier')}
                onChange={(v) => {
                  setParseRules({
                    ...parseRules,
                    unitMutiplier: v,
                  });
                }}
              />
            </div>
            {!isInAutoProgramming ?
              <Button
                onClick={() => {
                  rulesSubmit(parseRules, currentFileUri, productId);
                }}
              >
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('common.continue')}
                </span>
              </Button>
            :
              <Button
                onClick={() => {
                  // setIsConfigAutoProgramOpened(true);
                  handleRunSemiAutoProgram(
                    parseRules,
                    currentFileUri,
                    productId,
                    selectedInspectionTypes,
                    autoProgramInspectionRegion,
                  );
                }}
              >
                <span className='font-source text-[12px] font-semibold leading-[150%]'>
                  {t('autoProgramming.runAutoProgram')}
                </span>
              </Button>
            }
          </div>
        </div>
      </div>
    </div>
    </Fragment>
  );
};

export default Parse;